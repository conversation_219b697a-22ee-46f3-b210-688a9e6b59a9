<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HireNow Mobile App Mockups</title>
    <style>
        :root {
            --primary-color: #4A6FFF;
            --secondary-color: #63E2FF;
            --accent-color: #FF6B9A;
            --success-color: #4CD964;
            --warning-color: #FFCC00;
            --danger-color: #FF3B30;
            --background-color: #F8FAFF;
            --card-color: #FFFFFF;
            --text-primary: #333333;
            --text-secondary: #666666;
            --text-tertiary: #999999;
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        body {
            background: #F0F3F9;
            padding: 2rem;
            display: flex;
            flex-wrap: wrap;
            gap: 2rem;
            justify-content: center;
        }
        
        h1 {
            width: 100%;
            text-align: center;
            margin-bottom: 2rem;
            color: var(--text-primary);
            font-weight: 600;
        }
        
        .screen {
            width: 375px;
            height: 812px;
            background: var(--background-color);
            border-radius: 40px;
            overflow: hidden;
            position: relative;
            box-shadow: var(--shadow);
        }
        
        .screen-content {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .scrollable-content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 20px;
            padding-bottom: 30px;
        }
        
        .status-bar {
            height: 44px;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            background: transparent;
            color: var(--text-primary);
        }
        
        .screen-title {
            margin: 10px 0;
            font-size: 14px;
            text-align: center;
            color: var(--text-secondary);
            letter-spacing: 0.5px;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }
        
        .button {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 16px 24px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
        }
        
        .button:active {
            transform: translateY(0);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .button.primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }
        
        .button.secondary {
            background: white;
            color: var(--primary-color);
            border: 1px solid rgba(74, 111, 255, 0.2);
        }
        
        .card {
            background: var(--card-color);
            border-radius: 16px;
            padding: 20px;
            margin: 10px 20px;
            box-shadow: var(--shadow);
        }
        
        .job-card {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #EEF2FF, #D8E2FF);
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .tab-bar {
            height: 80px;
            display: flex;
            justify-content: space-around;
            align-items: center;
            background: white;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 12px;
            color: var(--text-tertiary);
            gap: 6px;
        }
        
        .tab-item.active {
            color: var(--primary-color);
        }
        
        .tab-icon {
            height: 24px;
            width: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--text-tertiary);
            font-size: 18px;
        }
        
        .tab-item.active .tab-icon {
            color: var(--primary-color);
        }
        
        .badge {
            background: var(--success-color);
            color: white;
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        
        .tag {
            background: rgba(74, 111, 255, 0.1);
            color: var(--primary-color);
            font-size: 12px;
            padding: 6px 12px;
            border-radius: 12px;
            font-weight: 500;
        }
        
        .interactive-element {
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .interactive-element:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }
        
        .interactive-element:active {
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <h1>HireNow Mobile App Mockups</h1>
    
    <div style="display: flex; flex-direction: column; align-items: center;">
        <!-- Welcome Screen -->
        <div style="margin-bottom: 10px; font-size: 18px; font-weight: 600; color: var(--primary-color);">Welcome Screen</div>
        <div class="screen">
            <div class="screen-content gradient-bg">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>●●● ■</span>
                </div>
                <div style="flex: 1; display: flex; flex-direction: column; justify-content: center; align-items: center; padding: 20px; text-align: center; overflow-y: auto;">
                    <div style="font-size: 32px; font-weight: 700; margin-bottom: 20px;">HireNow</div>
                    <div style="width: 200px; height: 200px; background: rgba(255, 255, 255, 0.2); border-radius: 50%; margin-bottom: 60px; display: flex; justify-content: center; align-items: center;">
                        <div style="font-size: 100px;">🤝</div>
                    </div>
                    <div style="font-size: 28px; font-weight: 600; margin-bottom: 20px;">Find Flexible Work, Anytime</div>
                    <div style="font-size: 16px; opacity: 0.8; margin-bottom: 40px;">Connect with local opportunities that fit your schedule and skills</div>
                    <div class="button" style="background: white; color: var(--primary-color); width: 80%; margin-bottom: 15px;">Get Started</div>
                    <div style="font-size: 14px; opacity: 0.8;">Already have an account? <span style="text-decoration: underline;">Sign In</span></div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="display: flex; flex-direction: column; align-items: center;">
        <!-- Login Screen -->
        <div style="margin-bottom: 10px; font-size: 18px; font-weight: 600; color: var(--primary-color);">Login Screen</div>
        <div class="screen">
            <div class="screen-content">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>●●● ■</span>
                </div>
                <div class="scrollable-content">
                    <div style="display: flex; flex-direction: column; align-items: center; margin-bottom: 40px;">
                        <div style="width: 100px; height: 100px; background: rgba(74, 111, 255, 0.1); border-radius: 50%; margin: 20px 0 30px; display: flex; justify-content: center; align-items: center;">
                            <div style="font-size: 50px;">🤝</div>
                        </div>
                        <div style="font-size: 28px; font-weight: 600; margin-bottom: 10px;">Welcome Back</div>
                        <div style="font-size: 16px; color: var(--text-secondary); text-align: center;">Log in to continue finding opportunities</div>
                    </div>
                    
                    <div style="margin-bottom: 20px;">
                        <div style="font-size: 14px; font-weight: 500; margin-bottom: 8px; margin-left: 4px;">Email</div>
                        <div style="background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1);">
                            <EMAIL>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <div style="font-size: 14px; font-weight: 500; margin-bottom: 8px; margin-left: 4px;">Password</div>
                        <div style="background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center;">
                            <span>••••••••</span>
                            <span style="color: var(--text-tertiary);">👁️</span>
                        </div>
                    </div>
                    
                    <div style="text-align: right; margin-bottom: 30px;">
                        <span style="font-size: 14px; color: var(--primary-color); font-weight: 500;">Forgot Password?</span>
                    </div>
                    
                    <div class="button primary" style="width: 100%; margin-bottom: 20px;">Log In</div>
                    
                    <div style="text-align: center; margin-bottom: 20px;">
                        <span style="font-size: 14px; color: var(--text-secondary);">Or continue with</span>
                    </div>
                    
                    <div style="display: flex; gap: 15px; margin-bottom: 30px;">
                        <div style="flex: 1; background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1); display: flex; justify-content: center; align-items: center; gap: 10px;">
                            <span style="font-size: 20px;">G</span>
                            <span style="font-size: 14px; font-weight: 500;">Google</span>
                        </div>
                        <div style="flex: 1; background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1); display: flex; justify-content: center; align-items: center; gap: 10px;">
                            <span style="font-size: 20px;">f</span>
                            <span style="font-size: 14px; font-weight: 500;">Facebook</span>
                        </div>
                    </div>
                    
                    <div style="text-align: center;">
                        <span style="font-size: 14px; color: var(--text-secondary);">Don't have an account? </span>
                        <span style="font-size: 14px; color: var(--primary-color); font-weight: 500;">Sign Up</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div style="display: flex; flex-direction: column; align-items: center;">
        <!-- Account Registration (Sign Up) Screen -->
        <div style="margin-bottom: 10px; font-size: 18px; font-weight: 600; color: var(--primary-color);">Sign Up Screen</div>
        <div class="screen">
            <div class="screen-content">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>●●● ■</span>
                </div>
                <div class="scrollable-content">
                    <div style="display: flex; flex-direction: column; align-items: center; margin-bottom: 40px;">
                        <div style="width: 100px; height: 100px; background: rgba(74, 111, 255, 0.1); border-radius: 50%; margin: 20px 0 30px; display: flex; justify-content: center; align-items: center;">
                            <div style="font-size: 50px;">🤝</div>
                        </div>
                        <div style="font-size: 28px; font-weight: 600; margin-bottom: 10px;">Create Account</div>
                        <div style="font-size: 16px; color: var(--text-secondary); text-align: center;">Sign up to find flexible work opportunities</div>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <div style="font-size: 14px; font-weight: 500; margin-bottom: 8px; margin-left: 4px;">Full Name (as per IC)</div>
                        <div style="background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1);">
                            John Doe
                        </div>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <div style="font-size: 14px; font-weight: 500; margin-bottom: 8px; margin-left: 4px;">Email Address</div>
                        <div style="background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1);">
                            <EMAIL>
                        </div>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <div style="font-size: 14px; font-weight: 500; margin-bottom: 8px; margin-left: 4px;">Phone Number</div>
                        <div style="display: flex; background: white; border-radius: 12px; border: 1px solid rgba(0,0,0,0.1);">
                            <div style="display: flex; align-items: center; padding: 0 12px; border-right: 1px solid #eee; min-width: 80px; color: var(--text-secondary); cursor: pointer;">
                                <span style="margin-right: 6px;">🇲🇾</span>
                                <span>+60</span>
                                <span style="margin-left: 6px; color: var(--text-tertiary); font-size: 12px;">▼</span>
                            </div>
                            <div style="flex: 1; padding: 16px; color: var(--text-primary);">************</div>
                        </div>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <div style="font-size: 14px; font-weight: 500; margin-bottom: 8px; margin-left: 4px;">Password</div>
                        <div style="background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center;">
                            <span>••••••••</span>
                            <span style="color: var(--text-tertiary);">👁️</span>
                        </div>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <div style="font-size: 14px; font-weight: 500; margin-bottom: 8px; margin-left: 4px;">Confirm Password</div>
                        <div style="background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center;">
                            <span>••••••••</span>
                            <span style="color: var(--text-tertiary);">👁️</span>
                        </div>
                    </div>
                    <div style="display: flex; align-items: flex-start; margin-bottom: 25px; gap: 10px;">
                        <div style="width: 22px; height: 22px; border-radius: 6px; border: 2px solid var(--primary-color); background: white; display: flex; justify-content: center; align-items: center; cursor: pointer; margin-top: 2px;">
                            <span style="color: var(--primary-color); font-size: 16px;">✓</span>
                        </div>
                        <div style="font-size: 14px; color: var(--text-secondary); line-height: 1.5;">
                            I agree to the <span style="color: var(--primary-color); text-decoration: underline; cursor: pointer;">Terms of Service</span> and <span style="color: var(--primary-color); text-decoration: underline; cursor: pointer;">Privacy Policy</span>
                        </div>
                    </div>
                    <div class="button primary" style="width: 100%; margin-bottom: 20px;">Create Account</div>
                    <div style="text-align: center;">
                        <span style="font-size: 14px; color: var(--text-secondary);">Already have an account? </span>
                        <span style="font-size: 14px; color: var(--primary-color); font-weight: 500; text-decoration: underline; cursor: pointer;">Log In</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="display: flex; flex-direction: column; align-items: center;">
        <!-- Role Selection Screen -->
        <div style="margin-bottom: 10px; font-size: 18px; font-weight: 600; color: var(--primary-color);">Role Selection</div>
        <div class="screen">
            <div class="screen-content">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>●●● ■</span>
                </div>
                <div class="scrollable-content">
                    <div style="text-align: center;">
                        <div style="font-size: 28px; font-weight: 600; margin-bottom: 15px;">I'm looking for...</div>
                        <div style="font-size: 16px; color: var(--text-secondary); margin-bottom: 40px;">Select how you want to use HireNow</div>
                        <div style="display: flex; flex-direction: column; gap: 20px;">
                            <div class="card" style="display: flex; align-items: center; padding: 30px; gap: 20px;">
                                <div style="width: 60px; height: 60px; border-radius: 16px; background: rgba(74, 111, 255, 0.1); display: flex; justify-content: center; align-items: center; font-size: 30px;">💼</div>
                                <div style="text-align: left; flex: 1;">
                                    <div style="font-size: 20px; font-weight: 600; margin-bottom: 5px;">Work Opportunities</div>
                                    <div style="font-size: 14px; color: var(--text-secondary);">Find flexible, part-time jobs</div>
                                </div>
                            </div>
                            <div class="card" style="display: flex; align-items: center; padding: 30px; gap: 20px;">
                                <div style="width: 60px; height: 60px; border-radius: 16px; background: rgba(255, 107, 154, 0.1); display: flex; justify-content: center; align-items: center; font-size: 30px;">🏢</div>
                                <div style="text-align: left; flex: 1;">
                                    <div style="font-size: 20px; font-weight: 600; margin-bottom: 5px;">Hiring Talent</div>
                                    <div style="font-size: 14px; color: var(--text-secondary);">Post jobs and find reliable workers</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="padding: 20px; margin-bottom: 20px;">
                    <div class="button primary" style="width: 100%;">Continue</div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="display: flex; flex-direction: column; align-items: center;">
        <!-- Profile Setup Screen -->
        <div style="margin-bottom: 10px; font-size: 18px; font-weight: 600; color: var(--primary-color);">Profile Setup</div>
        <div class="screen">
            <div class="screen-content">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>●●● ■</span>
                </div>
                <div class="scrollable-content">
                    <div style="display: flex; align-items: center; margin-bottom: 20px;">
                        <div style="flex: 1; height: 4px; background: var(--primary-color); border-radius: 2px;"></div>
                        <div style="flex: 2; height: 4px; background: rgba(74, 111, 255, 0.2); border-radius: 2px; margin-left: 5px;"></div>
                    </div>
                    <div style="font-size: 28px; font-weight: 600; margin-bottom: 15px;">Create your profile</div>
                    <div style="font-size: 16px; color: var(--text-secondary); margin-bottom: 30px;">Tell us about yourself so we can find the best opportunities for you</div>
                    
                    <div style="display: flex; justify-content: center; margin-bottom: 30px;">
                        <div style="position: relative;">
                            <div style="width: 100px; height: 100px; border-radius: 50%; background: #EEF2FF; display: flex; justify-content: center; align-items: center; font-size: 40px;">😊</div>
                            <div style="position: absolute; bottom: 0; right: 0; background: var(--primary-color); width: 30px; height: 30px; border-radius: 50%; display: flex; justify-content: center; align-items: center; color: white; font-size: 16px;">+</div>
                        </div>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <div style="font-size: 14px; font-weight: 500; margin-bottom: 8px;">Identification Card Number</div>
                        <div style="background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1);">
                            900101-14-5678
                        </div>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <div style="font-size: 14px; font-weight: 500; margin-bottom: 8px;">Current Address</div>
                        <div style="background: white; border-radius: 12px; padding: 0 0 0 0; border: none;">
                            <div style="margin-bottom: 10px;">
                                <div style="font-size: 13px; color: var(--text-tertiary); margin-bottom: 4px;">Address Line 1</div>
                                <div style="background: white; border-radius: 10px; padding: 12px; border: 1px solid rgba(0,0,0,0.1);">123 Main Street</div>
                            </div>
                            <div style="margin-bottom: 10px;">
                                <div style="font-size: 13px; color: var(--text-tertiary); margin-bottom: 4px;">Address Line 2</div>
                                <div style="background: white; border-radius: 10px; padding: 12px; border: 1px solid rgba(0,0,0,0.1);">Apt 4B</div>
                            </div>
                            <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                                <div style="flex: 1;">
                                    <div style="font-size: 13px; color: var(--text-tertiary); margin-bottom: 4px;">Postcode</div>
                                    <div style="background: white; border-radius: 10px; padding: 12px; border: 1px solid rgba(0,0,0,0.1);">50000</div>
                                </div>
                                <div style="flex: 2;">
                                    <div style="font-size: 13px; color: var(--text-tertiary); margin-bottom: 4px;">City</div>
                                    <div style="background: white; border-radius: 10px; padding: 12px; border: 1px solid rgba(0,0,0,0.1);">Kuala Lumpur</div>
                                </div>
                            </div>
                            <div style="margin-bottom: 0;">
                                <div style="font-size: 13px; color: var(--text-tertiary); margin-bottom: 4px;">State</div>
                                <div style="background: white; border-radius: 10px; padding: 12px; border: 1px solid rgba(0,0,0,0.1);">Wilayah Persekutuan</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="padding: 20px; margin-bottom: 20px;">
                    <div class="button primary" style="width: 100%;">Next</div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="display: flex; flex-direction: column; align-items: center;">
        <!-- Job Listings (Employer View) -->
        <div style="margin-bottom: 10px; font-size: 18px; font-weight: 600; color: var(--primary-color);">Job Listings (Employer)</div>
        <div class="screen">
            <div class="screen-content">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>●●● ■</span>
                </div>
                <div class="scrollable-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <div style="font-size: 28px; font-weight: 600;">Your Jobs</div>
                        <div style="background: var(--primary-color); width: 40px; height: 40px; border-radius: 12px; display: flex; justify-content: center; align-items: center; color: white; font-size: 20px;">+</div>
                    </div>
                    
                    <div style="display: flex; margin-bottom: 20px; overflow-x: auto; gap: 10px;">
                        <div style="background: var(--primary-color); color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; white-space: nowrap;">All Jobs</div>
                        <div style="background: white; color: var(--text-secondary); padding: 8px 16px; border-radius: 20px; font-size: 14px; white-space: nowrap;">Active (3)</div>
                        <div style="background: white; color: var(--text-secondary); padding: 8px 16px; border-radius: 20px; font-size: 14px; white-space: nowrap;">Expired (2)</div>
                        <div style="background: white; color: var(--text-secondary); padding: 8px 16px; border-radius: 20px; font-size: 14px; white-space: nowrap;">Drafts (1)</div>
                    </div>
                    
                    <div class="card job-card" style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="font-size: 18px; font-weight: 600;">Delivery Assistant</div>
                            <div class="badge">Active</div>
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <div class="tag">$25/hr</div>
                            <div class="tag">4 hours</div>
                        </div>
                        <div style="font-size: 14px; color: var(--text-secondary); margin-top: 5px;">San Francisco • Posted 2 days ago</div>
                        <div style="display: flex; justify-content: space-between; margin-top: 10px; border-top: 1px solid rgba(0,0,0,0.05); padding-top: 15px;">
                            <div style="font-size: 14px; color: var(--text-secondary);">12 applicants</div>
                            <div style="font-size: 14px; color: var(--primary-color); font-weight: 500;">View Details</div>
                        </div>
                    </div>
                    
                    <div class="card job-card" style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="font-size: 18px; font-weight: 600;">Event Staff</div>
                            <div class="badge">Active</div>
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <div class="tag">$22/hr</div>
                            <div class="tag">6 hours</div>
                        </div>
                        <div style="font-size: 14px; color: var(--text-secondary); margin-top: 5px;">San Francisco • Posted 5 days ago</div>
                        <div style="display: flex; justify-content: space-between; margin-top: 10px; border-top: 1px solid rgba(0,0,0,0.05); padding-top: 15px;">
                            <div style="font-size: 14px; color: var(--text-secondary);">8 applicants</div>
                            <div style="font-size: 14px; color: var(--primary-color); font-weight: 500;">View Details</div>
                        </div>
                    </div>
                    
                    <div class="card job-card">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="font-size: 18px; font-weight: 600;">Warehouse Helper</div>
                            <div class="badge" style="background: var(--warning-color);">Ending Soon</div>
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <div class="tag">$20/hr</div>
                            <div class="tag">8 hours</div>
                        </div>
                        <div style="font-size: 14px; color: var(--text-secondary); margin-top: 5px;">Oakland • Posted 1 week ago</div>
                        <div style="display: flex; justify-content: space-between; margin-top: 10px; border-top: 1px solid rgba(0,0,0,0.05); padding-top: 15px;">
                            <div style="font-size: 14px; color: var(--text-secondary);">4 applicants</div>
                            <div style="font-size: 14px; color: var(--primary-color); font-weight: 500;">View Details</div>
                        </div>
                    </div>
                </div>
                
                <div class="tab-bar">
                    <div class="tab-item">
                        <div class="tab-icon">🏠</div>
                        <div>Home</div>
                    </div>
                    <div class="tab-item active">
                        <div class="tab-icon">💼</div>
                        <div>Jobs</div>
                    </div>
                    <div class="tab-item">
                        <div class="tab-icon">💬</div>
                        <div>Messages</div>
                    </div>
                    <div class="tab-item">
                        <div class="tab-icon">👤</div>
                        <div>Profile</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="display: flex; flex-direction: column; align-items: center;">
        <!-- Browse Jobs (Job Seeker View) -->
        <div style="margin-bottom: 10px; font-size: 18px; font-weight: 600; color: var(--primary-color);">Browse Jobs (Job Seeker)</div>
        <div class="screen">
            <div class="screen-content">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>●●● ■</span>
                </div>
                <div class="scrollable-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <div>
                            <div style="font-size: 28px; font-weight: 600;">Find Jobs</div>
                            <div style="font-size: 14px; color: var(--text-secondary);">San Francisco, CA</div>
                        </div>
                        <div style="background: white; width: 40px; height: 40px; border-radius: 12px; display: flex; justify-content: center; align-items: center; color: var(--text-primary); font-size: 20px; box-shadow: var(--shadow);">🔍</div>
                    </div>
                    
                    <div style="position: relative; margin-bottom: 20px;">
                        <input type="text" placeholder="Search jobs..." style="width: 100%; padding: 16px; padding-left: 45px; border-radius: 12px; border: none; background: white; box-shadow: var(--shadow); font-size: 16px;">
                        <div style="position: absolute; left: 15px; top: 16px; color: var(--text-tertiary);">🔍</div>
                    </div>
                    
                    <div style="display: flex; margin-bottom: 20px; overflow-x: auto; gap: 10px;">
                        <div style="background: var(--primary-color); color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; white-space: nowrap;">All Jobs</div>
                        <div style="background: white; color: var(--text-secondary); padding: 8px 16px; border-radius: 20px; font-size: 14px; white-space: nowrap;">Nearby</div>
                        <div style="background: white; color: var(--text-secondary); padding: 8px 16px; border-radius: 20px; font-size: 14px; white-space: nowrap;">Recommended</div>
                        <div style="background: white; color: var(--text-secondary); padding: 8px 16px; border-radius: 20px; font-size: 14px; white-space: nowrap;">Saved</div>
                    </div>
                    
                    <div class="card job-card" style="margin-bottom: 15px;">
                        <div style="display: flex; gap: 15px;">
                            <div class="avatar">A</div>
                            <div style="flex: 1;">
                                <div style="font-size: 18px; font-weight: 600; margin-bottom: 5px;">Delivery Assistant</div>
                                <div style="display: flex; align-items: center; gap: 5px;">
                                    <div style="font-size: 16px; color: var(--text-secondary);">Acme Logistics</div>
                                    <div style="color: #FFD700; font-size: 14px;">★★★★★</div>
                                    <div style="font-size: 14px; color: var(--text-secondary);">(18)</div>
                                </div>
                                <div style="display: flex; gap: 10px; margin-bottom: 10px; margin-top: 5px;">
                                    <div class="tag">$25/hr</div>
                                    <div class="tag">4 hours</div>
                                </div>
                                <div style="font-size: 14px; color: var(--text-secondary);">2.5 miles away • Posted 2 days ago</div>
                            </div>
                            <div style="color: var(--text-tertiary); font-size: 20px;">♡</div>
                        </div>
                    </div>
                    
                    <div class="card job-card" style="margin-bottom: 15px;">
                        <div style="display: flex; gap: 15px;">
                            <div class="avatar">B</div>
                            <div style="flex: 1;">
                                <div style="font-size: 18px; font-weight: 600; margin-bottom: 5px;">Event Staff</div>
                                <div style="display: flex; align-items: center; gap: 5px;">
                                    <div style="font-size: 16px; color: var(--text-secondary);">Bay Area Events</div>
                                    <div style="color: #FFD700; font-size: 14px;">★★★★</div>
                                    <div style="font-size: 14px; color: var(--text-secondary);">(23)</div>
                                </div>
                                <div style="display: flex; gap: 10px; margin-bottom: 10px; margin-top: 5px;">
                                    <div class="tag">$22/hr</div>
                                    <div class="tag">6 hours</div>
                                </div>
                                <div style="font-size: 14px; color: var(--text-secondary);">1.2 miles away • Posted 5 days ago</div>
                            </div>
                            <div style="color: var(--text-tertiary); font-size: 20px;">♡</div>
                        </div>
                    </div>
                    
                    <div class="card job-card">
                        <div style="display: flex; gap: 15px;">
                            <div class="avatar">W</div>
                            <div style="flex: 1;">
                                <div style="font-size: 18px; font-weight: 600; margin-bottom: 5px;">Warehouse Helper</div>
                                <div style="display: flex; align-items: center; gap: 5px;">
                                    <div style="font-size: 16px; color: var(--text-secondary);">Warehouse Inc.</div>
                                    <div style="color: #FFD700; font-size: 14px;">★★★</div>
                                    <div style="font-size: 14px; color: var(--text-secondary);">(7)</div>
                                </div>
                                <div style="display: flex; gap: 10px; margin-bottom: 10px; margin-top: 5px;">
                                    <div class="tag">$20/hr</div>
                                    <div class="tag">8 hours</div>
                                </div>
                                <div style="font-size: 14px; color: var(--text-secondary);">5.7 miles away • Posted 1 week ago</div>
                            </div>
                            <div style="color: var(--accent-color); font-size: 20px;">♥</div>
                        </div>
                    </div>
                </div>
                
                <div class="tab-bar">
                    <div class="tab-item">
                        <div class="tab-icon">🏠</div>
                        <div>Home</div>
                    </div>
                    <div class="tab-item active">
                        <div class="tab-icon">🔍</div>
                        <div>Search</div>
                    </div>
                    <div class="tab-item">
                        <div class="tab-icon">📋</div>
                        <div>Applications</div>
                    </div>
                    <div class="tab-item">
                        <div class="tab-icon">👤</div>
                        <div>Profile</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="display: flex; flex-direction: column; align-items: center;">
        <!-- Application Management -->
        <div style="margin-bottom: 10px; font-size: 18px; font-weight: 600; color: var(--primary-color);">Application Management</div>
        <div class="screen">
            <div class="screen-content">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>●●● ■</span>
                </div>
                <div class="scrollable-content">
                    <div style="font-size: 28px; font-weight: 600; margin-bottom: 20px;">My Applications</div>
                    
                    <div style="display: flex; margin-bottom: 20px; overflow-x: auto; gap: 10px;">
                        <div style="background: var(--primary-color); color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; white-space: nowrap;">All</div>
                        <div style="background: white; color: var(--text-secondary); padding: 8px 16px; border-radius: 20px; font-size: 14px; white-space: nowrap;">Pending</div>
                        <div style="background: white; color: var(--text-secondary); padding: 8px 16px; border-radius: 20px; font-size: 14px; white-space: nowrap;">Accepted</div>
                        <div style="background: white; color: var(--text-secondary); padding: 8px 16px; border-radius: 20px; font-size: 14px; white-space: nowrap;">Completed</div>
                    </div>
                    
                    <div class="card job-card" style="margin-bottom: 15px;">
                        <div style="display: flex; gap: 15px;">
                            <div class="avatar">A</div>
                            <div style="flex: 1;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div style="font-size: 18px; font-weight: 600; margin-bottom: 5px;">Delivery Assistant</div>
                                    <div class="badge" style="background: var(--warning-color);">Pending</div>
                                </div>
                                <div style="font-size: 16px; color: var(--text-secondary); margin-bottom: 5px;">Acme Logistics</div>
                                <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                                    <div class="tag">$25/hr</div>
                                    <div class="tag">Tomorrow, 9 AM</div>
                                </div>
                                <div style="font-size: 14px; color: var(--text-secondary);">Applied 2 days ago</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card job-card" style="margin-bottom: 15px;">
                        <div style="display: flex; gap: 15px;">
                            <div class="avatar">B</div>
                            <div style="flex: 1;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div style="font-size: 18px; font-weight: 600; margin-bottom: 5px;">Event Staff</div>
                                    <div class="badge" style="background: var(--success-color);">Accepted</div>
                                </div>
                                <div style="font-size: 16px; color: var(--text-secondary); margin-bottom: 5px;">Bay Area Events</div>
                                <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                                    <div class="tag">$22/hr</div>
                                    <div class="tag">Friday, 5 PM</div>
                                </div>
                                <div style="font-size: 14px; color: var(--text-secondary);">Applied 5 days ago</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card job-card">
                        <div style="display: flex; gap: 15px;">
                            <div class="avatar">W</div>
                            <div style="flex: 1;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div style="font-size: 18px; font-weight: 600; margin-bottom: 5px;">Warehouse Helper</div>
                                    <div class="badge" style="background: var(--danger-color);">Declined</div>
                                </div>
                                <div style="font-size: 16px; color: var(--text-secondary); margin-bottom: 5px;">Warehouse Inc.</div>
                                <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                                    <div class="tag">$20/hr</div>
                                    <div class="tag">8 hours</div>
                                </div>
                                <div style="font-size: 14px; color: var(--text-secondary);">Applied 1 week ago</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="tab-bar">
                    <div class="tab-item">
                        <div class="tab-icon">🏠</div>
                        <div>Home</div>
                    </div>
                    <div class="tab-item">
                        <div class="tab-icon">🔍</div>
                        <div>Search</div>
                    </div>
                    <div class="tab-item active">
                        <div class="tab-icon">📋</div>
                        <div>Applications</div>
                    </div>
                    <div class="tab-item">
                        <div class="tab-icon">👤</div>
                        <div>Profile</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="display: flex; flex-direction: column; align-items: center;">
        <!-- Job History & Earnings -->
        <div style="margin-bottom: 10px; font-size: 18px; font-weight: 600; color: var(--primary-color);">Job History & Earnings</div>
        <div class="screen">
            <div class="screen-content">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>●●● ■</span>
                </div>
                <div class="scrollable-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <div style="font-size: 28px; font-weight: 600;">Earnings</div>
                        <div style="background: white; width: 40px; height: 40px; border-radius: 12px; display: flex; justify-content: center; align-items: center; color: var(--text-primary); font-size: 20px; box-shadow: var(--shadow);">📅</div>
                    </div>
                    
                    <div class="card" style="margin-bottom: 20px;">
                        <div style="font-size: 16px; color: var(--text-secondary); margin-bottom: 10px;">Total Earnings</div>
                        <div style="font-size: 36px; font-weight: 600; color: var(--primary-color); margin-bottom: 15px;">$1,245.00</div>
                        
                        <div style="display: flex; gap: 15px; margin-bottom: 20px;">
                            <div style="flex: 1; background: rgba(74, 111, 255, 0.1); padding: 15px; border-radius: 12px; text-align: center;">
                                <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 5px;">This Week</div>
                                <div style="font-size: 20px; font-weight: 600; color: var(--primary-color);">$180</div>
                            </div>
                            <div style="flex: 1; background: rgba(74, 111, 255, 0.1); padding: 15px; border-radius: 12px; text-align: center;">
                                <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 5px;">This Month</div>
                                <div style="font-size: 20px; font-weight: 600; color: var(--primary-color);">$685</div>
                            </div>
                        </div>
                        
                        <div style="display: flex; justify-content: center;">
                            <div class="button primary" style="padding: 12px 20px;">Withdraw to Bank</div>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 15px; font-size: 20px; font-weight: 600;">Recent Jobs</div>
                    
                    <div class="card job-card" style="margin-bottom: 15px;">
                        <div style="display: flex; gap: 15px;">
                            <div class="avatar">B</div>
                            <div style="flex: 1;">
                                <div style="font-size: 18px; font-weight: 600; margin-bottom: 5px;">Event Staff</div>
                                <div style="font-size: 16px; color: var(--text-secondary); margin-bottom: 5px;">Bay Area Events</div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                    <div style="font-size: 14px; color: var(--text-secondary);">July 15, 2023</div>
                                    <div style="font-size: 18px; font-weight: 600; color: var(--primary-color);">$132</div>
                                </div>
                                <div style="font-size: 14px; color: var(--success-color); font-weight: 500;">Completed • 6 hours</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card job-card" style="margin-bottom: 15px;">
                        <div style="display: flex; gap: 15px;">
                            <div class="avatar">A</div>
                            <div style="flex: 1;">
                                <div style="font-size: 18px; font-weight: 600; margin-bottom: 5px;">Delivery Assistant</div>
                                <div style="font-size: 16px; color: var(--text-secondary); margin-bottom: 5px;">Acme Logistics</div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                    <div style="font-size: 14px; color: var(--text-secondary);">July 10, 2023</div>
                                    <div style="font-size: 18px; font-weight: 600; color: var(--primary-color);">$100</div>
                                </div>
                                <div style="font-size: 14px; color: var(--success-color); font-weight: 500;">Completed • 4 hours</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card job-card">
                        <div style="display: flex; gap: 15px;">
                            <div class="avatar">S</div>
                            <div style="flex: 1;">
                                <div style="font-size: 18px; font-weight: 600; margin-bottom: 5px;">Store Associate</div>
                                <div style="font-size: 16px; color: var(--text-secondary); margin-bottom: 5px;">SuperMart</div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                    <div style="font-size: 14px; color: var(--text-secondary);">July 5, 2023</div>
                                    <div style="font-size: 18px; font-weight: 600; color: var(--primary-color);">$92</div>
                                </div>
                                <div style="font-size: 14px; color: var(--success-color); font-weight: 500;">Completed • 4 hours</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="tab-bar">
                    <div class="tab-item">
                        <div class="tab-icon">🏠</div>
                        <div>Home</div>
                    </div>
                    <div class="tab-item">
                        <div class="tab-icon">🔍</div>
                        <div>Search</div>
                    </div>
                    <div class="tab-item">
                        <div class="tab-icon">📋</div>
                        <div>Applications</div>
                    </div>
                    <div class="tab-item active">
                        <div class="tab-icon">👤</div>
                        <div>Profile</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="display: flex; flex-direction: column; align-items: center;">
        <!-- Notifications Center -->
        <div style="margin-bottom: 10px; font-size: 18px; font-weight: 600; color: var(--primary-color);">Notifications Center</div>
        <div class="screen">
            <div class="screen-content">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>●●● ■</span>
                </div>
                <div class="scrollable-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <div style="font-size: 28px; font-weight: 600;">Notifications</div>
                        <div style="font-size: 14px; color: var(--primary-color); font-weight: 500;">Mark all read</div>
                    </div>
                    
                    <div style="display: flex; margin-bottom: 20px; overflow-x: auto; gap: 10px;">
                        <div style="background: var(--primary-color); color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; white-space: nowrap;">All</div>
                        <div style="background: white; color: var(--text-secondary); padding: 8px 16px; border-radius: 20px; font-size: 14px; white-space: nowrap;">Jobs</div>
                        <div style="background: white; color: var(--text-secondary); padding: 8px 16px; border-radius: 20px; font-size: 14px; white-space: nowrap;">Applications</div>
                        <div style="background: white; color: var(--text-secondary); padding: 8px 16px; border-radius: 20px; font-size: 14px; white-space: nowrap;">Payments</div>
                    </div>
                    
                    <div style="margin-bottom: 15px; font-size: 16px; color: var(--text-secondary);">Today</div>
                    
                    <div class="card" style="margin-bottom: 15px; padding: 15px; display: flex; gap: 15px; background: rgba(74, 111, 255, 0.05); border-left: 4px solid var(--primary-color);">
                        <div style="width: 50px; height: 50px; border-radius: 15px; background: var(--primary-color); display: flex; justify-content: center; align-items: center; color: white; font-size: 20px;">✓</div>
                        <div style="flex: 1;">
                            <div style="font-size: 16px; font-weight: 600; margin-bottom: 5px;">Application Accepted</div>
                            <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 5px;">Your application for Event Staff at Bay Area Events has been accepted!</div>
                            <div style="font-size: 12px; color: var(--text-tertiary);">2 hours ago</div>
                        </div>
                    </div>
                    
                    <div class="card" style="margin-bottom: 15px; padding: 15px; display: flex; gap: 15px;">
                        <div style="width: 50px; height: 50px; border-radius: 15px; background: rgba(76, 217, 100, 0.1); display: flex; justify-content: center; align-items: center; color: var(--success-color); font-size: 20px;">💰</div>
                        <div style="flex: 1;">
                            <div style="font-size: 16px; font-weight: 600; margin-bottom: 5px;">Payment Received</div>
                            <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 5px;">You received a payment of $132 for your work at Bay Area Events.</div>
                            <div style="font-size: 12px; color: var(--text-tertiary);">5 hours ago</div>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 15px; margin-top: 25px; font-size: 16px; color: var(--text-secondary);">Yesterday</div>
                    
                    <div class="card" style="margin-bottom: 15px; padding: 15px; display: flex; gap: 15px;">
                        <div style="width: 50px; height: 50px; border-radius: 15px; background: rgba(255, 59, 48, 0.1); display: flex; justify-content: center; align-items: center; color: var(--danger-color); font-size: 20px;">✕</div>
                        <div style="flex: 1;">
                            <div style="font-size: 16px; font-weight: 600; margin-bottom: 5px;">Application Declined</div>
                            <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 5px;">Your application for Warehouse Helper at Warehouse Inc. was not accepted.</div>
                            <div style="font-size: 12px; color: var(--text-tertiary);">Yesterday, 4:30 PM</div>
                        </div>
                    </div>
                    
                    <div class="card" style="margin-bottom: 15px; padding: 15px; display: flex; gap: 15px;">
                        <div style="width: 50px; height: 50px; border-radius: 15px; background: rgba(74, 111, 255, 0.1); display: flex; justify-content: center; align-items: center; color: var(--primary-color); font-size: 20px;">📋</div>
                        <div style="flex: 1;">
                            <div style="font-size: 16px; font-weight: 600; margin-bottom: 5px;">New Job Match</div>
                            <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 5px;">We found a new job that matches your skills: Store Associate at Tech Mart.</div>
                            <div style="font-size: 12px; color: var(--text-tertiary);">Yesterday, 10:15 AM</div>
                        </div>
                    </div>
                </div>
                
                <div class="tab-bar">
                    <div class="tab-item">
                        <div class="tab-icon">🏠</div>
                        <div>Home</div>
                    </div>
                    <div class="tab-item">
                        <div class="tab-icon">🔍</div>
                        <div>Search</div>
                    </div>
                    <div class="tab-item">
                        <div class="tab-icon">📋</div>
                        <div>Applications</div>
                    </div>
                    <div class="tab-item active">
                        <div class="tab-icon">👤</div>
                        <div>Profile</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="display: flex; flex-direction: column; align-items: center;">
        <!-- Location-based Job Recommender -->
        <div style="margin-bottom: 10px; font-size: 18px; font-weight: 600; color: var(--primary-color);">Location-based Job Recommender</div>
        <div class="screen">
            <div class="screen-content">
                <div class="status-bar" style="background: rgba(255, 255, 255, 0.9); z-index: 10;">
                    <span>9:41</span>
                    <span>●●● ■</span>
                </div>
                <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: #E5EDFC;">
                    <!-- Map Background -->
                    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-color: #D8E2FF;">
                        <!-- Map Grid -->
                        <div style="position: absolute; left: 50px; top: 100px; right: 50px; bottom: 100px; background-color: #E5EDFC;"></div>
                        <div style="position: absolute; left: 120px; top: 100px; width: 20px; bottom: 100px; background-color: #C4D1FF;"></div>
                        <div style="position: absolute; left: 200px; top: 100px; width: 20px; bottom: 100px; background-color: #C4D1FF;"></div>
                        <div style="position: absolute; left: 280px; top: 100px; width: 20px; bottom: 100px; background-color: #C4D1FF;"></div>
                        <div style="position: absolute; left: 50px; top: 180px; right: 50px; height: 20px; background-color: #C4D1FF;"></div>
                        <div style="position: absolute; left: 50px; top: 280px; right: 50px; height: 20px; background-color: #C4D1FF;"></div>
                        <div style="position: absolute; left: 50px; top: 380px; right: 50px; height: 20px; background-color: #C4D1FF;"></div>
                        <div style="position: absolute; left: 50px; top: 480px; right: 50px; height: 20px; background-color: #C4D1FF;"></div>
                        <div style="position: absolute; left: 50px; top: 580px; right: 50px; height: 20px; background-color: #C4D1FF;"></div>
                    </div>
                    
                    <!-- Job Markers -->
                    <div style="position: absolute; top: 250px; left: 100px; width: 40px; height: 40px; background: var(--primary-color); border-radius: 20px; display: flex; justify-content: center; align-items: center; color: white; font-size: 18px; box-shadow: var(--shadow);">$25</div>
                    
                    <div style="position: absolute; top: 350px; left: 220px; width: 40px; height: 40px; background: var(--accent-color); border-radius: 20px; display: flex; justify-content: center; align-items: center; color: white; font-size: 18px; box-shadow: var(--shadow);">$22</div>
                    
                    <div style="position: absolute; top: 450px; left: 150px; width: 40px; height: 40px; background: var(--primary-color); border-radius: 20px; display: flex; justify-content: center; align-items: center; color: white; font-size: 18px; box-shadow: var(--shadow);">$20</div>
                    
                    <!-- Current Location -->
                    <div style="position: absolute; top: 400px; left: 187px; width: 30px; height: 30px; background: white; border: 3px solid var(--primary-color); border-radius: 15px; box-shadow: var(--shadow);"></div>
                    
                    <!-- Bottom Sheet -->
                    <div style="position: absolute; bottom: 0; left: 0; width: 100%; height: 60%; background: white; border-radius: 30px 30px 0 0; box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1); padding: 20px; display: flex; flex-direction: column;">
                        <div style="width: 40px; height: 5px; background: #DDD; border-radius: 3px; margin: 0 auto 20px;"></div>
                        
                        <div style="font-size: 22px; font-weight: 600; margin-bottom: 15px;">Nearby Jobs</div>
                        
                        <div style="overflow-y: auto; flex: 1;">
                            <div class="card job-card" style="margin-bottom: 15px; padding: 15px;">
                                <div style="display: flex; gap: 15px;">
                                    <div class="avatar">A</div>
                                    <div style="flex: 1;">
                                        <div style="font-size: 18px; font-weight: 600; margin-bottom: 5px;">Delivery Assistant</div>
                                        <div style="font-size: 16px; color: var(--text-secondary); margin-bottom: 5px;">Acme Logistics</div>
                                        <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                                            <div class="tag">$25/hr</div>
                                            <div class="tag">1.2 mi</div>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; margin-top: 10px;">
                                            <div style="font-size: 14px; color: var(--text-secondary);">Tomorrow, 9 AM - 1 PM</div>
                                            <div class="button primary" style="padding: 8px 16px; font-size: 14px;">Apply</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card job-card" style="margin-bottom: 15px; padding: 15px; background: rgba(255, 107, 154, 0.05); border-left: 4px solid var(--accent-color);">
                                <div style="display: flex; gap: 15px;">
                                    <div class="avatar">B</div>
                                    <div style="flex: 1;">
                                        <div style="font-size: 18px; font-weight: 600; margin-bottom: 5px;">Event Staff</div>
                                        <div style="font-size: 16px; color: var(--text-secondary); margin-bottom: 5px;">Bay Area Events</div>
                                        <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                                            <div class="tag">$22/hr</div>
                                            <div class="tag">0.8 mi</div>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; margin-top: 10px;">
                                            <div style="font-size: 14px; color: var(--text-secondary);">Friday, 5 PM - 11 PM</div>
                                            <div class="button primary" style="padding: 8px 16px; font-size: 14px;">Apply</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card job-card" style="padding: 15px;">
                                <div style="display: flex; gap: 15px;">
                                    <div class="avatar">W</div>
                                    <div style="flex: 1;">
                                        <div style="font-size: 18px; font-weight: 600; margin-bottom: 5px;">Warehouse Helper</div>
                                        <div style="font-size: 16px; color: var(--text-secondary); margin-bottom: 5px;">Warehouse Inc.</div>
                                        <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                                            <div class="tag">$20/hr</div>
                                            <div class="tag">1.5 mi</div>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; margin-top: 10px;">
                                            <div style="font-size: 14px; color: var(--text-secondary);">Monday, 8 AM - 4 PM</div>
                                            <div class="button primary" style="padding: 8px 16px; font-size: 14px;">Apply</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Search Button -->
                    <div style="position: absolute; top: 70px; right: 20px; background: white; width: 50px; height: 50px; border-radius: 25px; display: flex; justify-content: center; align-items: center; color: var(--text-primary); font-size: 20px; box-shadow: var(--shadow);">🔍</div>
                    
                    <!-- Filter Button -->
                    <div style="position: absolute; top: 130px; right: 20px; background: white; width: 50px; height: 50px; border-radius: 25px; display: flex; justify-content: center; align-items: center; color: var(--text-primary); font-size: 20px; box-shadow: var(--shadow);">⚙️</div>
                    
                    <!-- Location Button -->
                    <div style="position: absolute; top: 190px; right: 20px; background: var(--primary-color); width: 50px; height: 50px; border-radius: 25px; display: flex; justify-content: center; align-items: center; color: white; font-size: 20px; box-shadow: var(--shadow);">📍</div>
                </div>
                
                <div class="tab-bar">
                    <div class="tab-item">
                        <div class="tab-icon">🏠</div>
                        <div>Home</div>
                    </div>
                    <div class="tab-item active">
                        <div class="tab-icon">🔍</div>
                        <div>Search</div>
                    </div>
                    <div class="tab-item">
                        <div class="tab-icon">📋</div>
                        <div>Applications</div>
                    </div>
                    <div class="tab-item">
                        <div class="tab-icon">👤</div>
                        <div>Profile</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="display: flex; flex-direction: column; align-items: center;">
        <!-- User Profile Screen -->
        <div style="margin-bottom: 10px; font-size: 18px; font-weight: 600; color: var(--primary-color);">User Profile Screen</div>
        <div class="screen">
            <div class="screen-content">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>●●● ■</span>
                </div>
                <div class="scrollable-content">
                    <!-- Profile Header -->
                    <div style="display: flex; flex-direction: column; align-items: center; margin-bottom: 25px;">
                        <div style="position: relative; margin-bottom: 10px;">
                            <div style="width: 120px; height: 120px; border-radius: 60px; background: #EEF2FF; display: flex; justify-content: center; align-items: center; font-size: 50px; border: 4px solid white; box-shadow: var(--shadow);">
                                😊
                            </div>
                            <div style="position: absolute; bottom: 5px; right: 5px; background: var(--primary-color); width: 32px; height: 32px; border-radius: 16px; display: flex; justify-content: center; align-items: center; color: white; font-size: 16px; box-shadow: var(--shadow);">
                                ✏️
                            </div>
                        </div>
                        <div style="font-size: 24px; font-weight: 600; margin-bottom: 5px;">John Doe</div>
                        <div style="font-size: 16px; color: var(--text-secondary); margin-bottom: 15px;">San Francisco, CA</div>
                        <div style="display: flex; align-items: center; gap: 5px; margin-bottom: 5px;">
                            <div style="color: #FFD700; font-size: 18px;">★★★★</div>
                            <div style="color: #DDD; font-size: 18px;">★</div>
                            <div style="font-size: 16px; font-weight: 500; margin-left: 5px;">4.0</div>
                        </div>
                        <div style="font-size: 14px; color: var(--text-secondary);">(26 reviews)</div>
                    </div>
                    
                    <!-- Profile Tabs -->
                    <div style="display: flex; border-bottom: 1px solid rgba(0,0,0,0.1); margin-bottom: 20px;">
                        <div style="flex: 1; text-align: center; padding: 15px 10px; border-bottom: 3px solid var(--primary-color); color: var(--primary-color); font-weight: 600;">About</div>
                        <div style="flex: 1; text-align: center; padding: 15px 10px; color: var(--text-secondary);">Reviews</div>
                        <div style="flex: 1; text-align: center; padding: 15px 10px; color: var(--text-secondary);">History</div>
                    </div>
                    
                    <!-- About Section -->
                    <div style="margin-bottom: 25px;">
                        <div style="font-size: 18px; font-weight: 600; margin-bottom: 15px;">About Me</div>
                        <div style="font-size: 15px; color: var(--text-secondary); line-height: 1.5; margin-bottom: 20px;">
                            Reliable and enthusiastic worker with experience in customer service, delivery, and warehouse operations. Looking for flexible work opportunities to match my schedule.
                        </div>
                    </div>
                    
                    <!-- Skills Section -->
                    <div style="margin-bottom: 25px;">
                        <div style="font-size: 18px; font-weight: 600; margin-bottom: 15px;">Skills</div>
                        <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 10px;">
                            <div class="tag">Customer Service</div>
                            <div class="tag">Delivery</div>
                            <div class="tag">Warehouse</div>
                            <div class="tag">Inventory</div>
                            <div class="tag">Event Staff</div>
                        </div>
                    </div>
                    
                    <!-- Availability Section -->
                    <div style="margin-bottom: 25px;">
                        <div style="font-size: 18px; font-weight: 600; margin-bottom: 15px;">Availability</div>
                        <div class="card" style="margin: 0;">
                            <div style="display: flex; margin-bottom: 15px;">
                                <div style="flex: 1; text-align: center; font-weight: 500;">Mon</div>
                                <div style="flex: 1; text-align: center; font-weight: 500;">Tue</div>
                                <div style="flex: 1; text-align: center; font-weight: 500;">Wed</div>
                                <div style="flex: 1; text-align: center; font-weight: 500;">Thu</div>
                                <div style="flex: 1; text-align: center; font-weight: 500;">Fri</div>
                                <div style="flex: 1; text-align: center; font-weight: 500;">Sat</div>
                                <div style="flex: 1; text-align: center; font-weight: 500;">Sun</div>
                            </div>
                            <div style="display: flex;">
                                <div style="flex: 1; text-align: center;">
                                    <div style="width: 30px; height: 30px; margin: 0 auto; background: rgba(74, 111, 255, 0.2); border-radius: 50%; display: flex; justify-content: center; align-items: center;">✓</div>
                                </div>
                                <div style="flex: 1; text-align: center;">
                                    <div style="width: 30px; height: 30px; margin: 0 auto; background: rgba(74, 111, 255, 0.2); border-radius: 50%; display: flex; justify-content: center; align-items: center;">✓</div>
                                </div>
                                <div style="flex: 1; text-align: center;">
                                    <div style="width: 30px; height: 30px; margin: 0 auto; background: white; border: 1px solid #ddd; border-radius: 50%;"></div>
                                </div>
                                <div style="flex: 1; text-align: center;">
                                    <div style="width: 30px; height: 30px; margin: 0 auto; background: rgba(74, 111, 255, 0.2); border-radius: 50%; display: flex; justify-content: center; align-items: center;">✓</div>
                                </div>
                                <div style="flex: 1; text-align: center;">
                                    <div style="width: 30px; height: 30px; margin: 0 auto; background: rgba(74, 111, 255, 0.2); border-radius: 50%; display: flex; justify-content: center; align-items: center;">✓</div>
                                </div>
                                <div style="flex: 1; text-align: center;">
                                    <div style="width: 30px; height: 30px; margin: 0 auto; background: rgba(74, 111, 255, 0.2); border-radius: 50%; display: flex; justify-content: center; align-items: center;">✓</div>
                                </div>
                                <div style="flex: 1; text-align: center;">
                                    <div style="width: 30px; height: 30px; margin: 0 auto; background: white; border: 1px solid #ddd; border-radius: 50%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Reviews Section -->
                    <div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <div style="font-size: 18px; font-weight: 600;">Recent Reviews</div>
                            <div style="font-size: 14px; color: var(--primary-color); font-weight: 500;">See All</div>
                        </div>
                        
                        <div class="card" style="margin-bottom: 15px; padding: 15px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <div class="avatar">A</div>
                                    <div>
                                        <div style="font-weight: 600;">Acme Logistics</div>
                                        <div style="font-size: 12px; color: var(--text-tertiary);">2 weeks ago</div>
                                    </div>
                                </div>
                                <div style="color: #FFD700; font-size: 16px;">★★★★★</div>
                            </div>
                            <div style="font-size: 14px; color: var(--text-secondary); line-height: 1.4;">
                                John was extremely reliable and completed all tasks efficiently. Would definitely hire again!
                            </div>
                        </div>
                        
                        <div class="card" style="padding: 15px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <div class="avatar">B</div>
                                    <div>
                                        <div style="font-weight: 600;">Bay Area Events</div>
                                        <div style="font-size: 12px; color: var(--text-tertiary);">1 month ago</div>
                                    </div>
                                </div>
                                <div style="color: #FFD700; font-size: 16px;">★★★</div>
                            </div>
                            <div style="font-size: 14px; color: var(--text-secondary); line-height: 1.4;">
                                Good worker but arrived a bit late. Otherwise did the job well and was friendly with guests.
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="tab-bar">
                    <div class="tab-item">
                        <div class="tab-icon">🏠</div>
                        <div>Home</div>
                    </div>
                    <div class="tab-item">
                        <div class="tab-icon">🔍</div>
                        <div>Search</div>
                    </div>
                    <div class="tab-item">
                        <div class="tab-icon">📋</div>
                        <div>Applications</div>
                    </div>
                    <div class="tab-item active">
                        <div class="tab-icon">👤</div>
                        <div>Profile</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="display: flex; flex-direction: column; align-items: center;">
        <!-- Rating & Review Screen -->
        <div style="margin-bottom: 10px; font-size: 18px; font-weight: 600; color: var(--primary-color);">Rating & Review Screen</div>
        <div class="screen">
            <div class="screen-content">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>●●● ■</span>
                </div>
                <div style="padding: 20px; display: flex; align-items: center; gap: 10px;">
                    <div style="width: 40px; height: 40px; border-radius: 20px; background: rgba(74, 111, 255, 0.1); display: flex; justify-content: center; align-items: center; font-size: 18px;">
                        ←
                    </div>
                    <div style="font-size: 18px; font-weight: 600;">Leave a Review</div>
                </div>
                <div class="scrollable-content">
                    <!-- Job and Person Info -->
                    <div class="card" style="margin-bottom: 25px;">
                        <div style="display: flex; gap: 15px; align-items: center; margin-bottom: 15px;">
                            <div class="avatar">B</div>
                            <div style="flex: 1;">
                                <div style="font-size: 18px; font-weight: 600; margin-bottom: 5px;">Event Staff</div>
                                <div style="font-size: 16px; color: var(--text-secondary); margin-bottom: 5px;">Bay Area Events</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 10px; padding-top: 15px; border-top: 1px solid rgba(0,0,0,0.05);">
                            <div style="width: 40px; height: 40px; border-radius: 20px; background: #EEF2FF; display: flex; justify-content: center; align-items: center; font-size: 20px;">
                                😊
                            </div>
                            <div>
                                <div style="font-size: 16px; font-weight: 500;">John Doe</div>
                                <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 5px;">July 15, 2023 • 6 hours</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Rating Section -->
                    <div style="text-align: center; margin-bottom: 30px;">
                        <div style="font-size: 20px; font-weight: 600; margin-bottom: 20px;">How was John's performance?</div>
                        <div style="font-size: 40px; letter-spacing: 10px; margin-bottom: 10px;">
                            <span style="color: #FFD700;">★★★★</span><span style="color: #DDD;">★</span>
                        </div>
                        <div style="font-size: 16px; color: var(--text-secondary);">4.0 out of 5</div>
                    </div>
                    
                    <!-- Rating Categories -->
                    <div style="margin-bottom: 30px;">
                        <div style="margin-bottom: 15px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <div style="font-size: 16px; font-weight: 500;">Punctuality</div>
                                <div style="font-size: 16px; color: #FFD700;">★★★★★</div>
                            </div>
                            <div style="height: 6px; background: #EEF2FF; border-radius: 3px;">
                                <div style="height: 6px; width: 100%; background: var(--primary-color); border-radius: 3px;"></div>
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 15px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <div style="font-size: 16px; font-weight: 500;">Communication</div>
                                <div style="font-size: 16px; color: #FFD700;">★★★★</div>
                            </div>
                            <div style="height: 6px; background: #EEF2FF; border-radius: 3px;">
                                <div style="height: 6px; width: 80%; background: var(--primary-color); border-radius: 3px;"></div>
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 15px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                <div style="font-size: 16px; font-weight: 500;">Work Quality</div>
                                <div style="font-size: 16px; color: #FFD700;">★★★</div>
                            </div>
                            <div style="height: 6px; background: #EEF2FF; border-radius: 3px;">
                                <div style="height: 6px; width: 60%; background: var(--primary-color); border-radius: 3px;"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Written Review -->
                    <div style="margin-bottom: 30px;">
                        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">Write a Review</div>
                        <div style="background: white; border-radius: 12px; padding: 15px; border: 1px solid rgba(0,0,0,0.1); height: 120px; font-size: 14px; color: var(--text-secondary);">
                            John was on time and handled all his duties well. He was great with customers and followed all instructions carefully. Would recommend for future events.
                        </div>
                    </div>
                    
                    <!-- Tips Section -->
                    <div style="margin-bottom: 30px;">
                        <div style="font-size: 16px; font-weight: 500; margin-bottom: 15px;">Add a Tip (Optional)</div>
                        <div style="display: flex; gap: 10px; justify-content: center;">
                            <div style="flex: 1; padding: 15px; border-radius: 12px; background: rgba(74, 111, 255, 0.1); text-align: center; color: var(--text-secondary);">$5</div>
                            <div style="flex: 1; padding: 15px; border-radius: 12px; border: 2px solid var(--primary-color); text-align: center; color: var(--primary-color); font-weight: 500;">$10</div>
                            <div style="flex: 1; padding: 15px; border-radius: 12px; background: rgba(74, 111, 255, 0.1); text-align: center; color: var(--text-secondary);">$15</div>
                            <div style="flex: 1; padding: 15px; border-radius: 12px; background: rgba(74, 111, 255, 0.1); text-align: center; color: var(--text-secondary);">Custom</div>
                        </div>
                    </div>
                </div>
                
                <div style="padding: 20px; margin-top: auto;">
                    <div class="button primary" style="width: 100%;">Submit Review</div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="display: flex; flex-direction: column; align-items: center;">
        <!-- Create New Job Screen (Step 1) -->
        <div style="margin-bottom: 10px; font-size: 18px; font-weight: 600; color: var(--primary-color);">Create New Job - Step 1</div>
        <div class="screen">
            <div class="screen-content">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>●●● ■</span>
                </div>
                <div style="padding: 20px; display: flex; align-items: center; gap: 10px;">
                    <div style="width: 40px; height: 40px; border-radius: 20px; background: rgba(74, 111, 255, 0.1); display: flex; justify-content: center; align-items: center; font-size: 18px;">
                        ←
                    </div>
                    <div style="font-size: 18px; font-weight: 600;">Post a New Job</div>
                </div>
                <div class="scrollable-content">
                    <!-- Job Creation Form -->
                    <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
                        <div style="width: 30%;">
                            <div style="width: 60px; height: 60px; border-radius: 16px; background: rgba(74, 111, 255, 0.1); display: flex; justify-content: center; align-items: center; font-size: 30px;">
                                📋
                            </div>
                        </div>
                        <div style="width: 70%; display: flex; flex-direction: column; justify-content: center;">
                            <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 5px;">Step 1 of 3</div>
                            <div style="height: 6px; background: #EEF2FF; border-radius: 3px; width: 100%;">
                                <div style="height: 6px; width: 33%; background: var(--primary-color); border-radius: 3px;"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Job Title Section -->
                    <div style="margin-bottom: 25px;">
                        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">Job Title*</div>
                        <div style="background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1);">
                            Event Staff
                        </div>
                    </div>
                    
                    <!-- Job Category Section -->
                    <div style="margin-bottom: 25px;">
                        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">Category*</div>
                        <div style="background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center;">
                            <span>Hospitality & Events</span>
                            <span style="font-size: 14px; color: var(--text-tertiary);">▼</span>
                        </div>
                    </div>
                    
                    <!-- Job Description Section -->
                    <div style="margin-bottom: 25px;">
                        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">Description*</div>
                        <div style="background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1); height: 120px; font-size: 14px; color: var(--text-secondary);">
                            We are looking for reliable event staff to assist with a corporate event. Duties include guest check-in, serving food and drinks, and general event support.
                        </div>
                    </div>
                    
                    <!-- Compensation Section -->
                    <div style="margin-bottom: 25px;">
                        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">Hourly Pay*</div>
                        <div style="display: flex; align-items: center; background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1);">
                            <div style="margin-right: 10px; font-size: 16px; color: var(--text-tertiary);">$</div>
                            <div>22.00</div>
                        </div>
                    </div>
                    
                    <!-- Location Section -->
                    <div style="margin-bottom: 25px;">
                        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">Location*</div>
                        <div style="background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center;">
                            <span>San Francisco, CA</span>
                            <span style="color: var(--primary-color);">📍</span>
                        </div>
                    </div>
                    
                    <!-- Skills Required Section -->
                    <div style="margin-bottom: 25px;">
                        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">Required Skills</div>
                        <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 15px;">
                            <div class="tag" style="display: flex; align-items: center; gap: 5px;">Customer Service <span style="font-size: 12px;">✕</span></div>
                            <div class="tag" style="display: flex; align-items: center; gap: 5px;">Hospitality <span style="font-size: 12px;">✕</span></div>
                            <div class="tag" style="display: flex; align-items: center; gap: 5px;">Communication <span style="font-size: 12px;">✕</span></div>
                        </div>
                        <div style="background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center;">
                            <span style="color: var(--text-tertiary);">Add skill...</span>
                            <span style="color: var(--primary-color); font-size: 20px;">+</span>
                        </div>
                    </div>
                    
                    <!-- Date and Time Section -->
                    <div style="margin-bottom: 25px;">
                        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">Date & Time*</div>
                        <div style="background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1); margin-bottom: 10px; display: flex; justify-content: space-between; align-items: center;">
                            <span>Friday, July 28, 2023</span>
                            <span style="color: var(--text-tertiary); font-size: 16px;">📅</span>
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <div style="flex: 1; background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center;">
                                <span>5:00 PM</span>
                                <span style="color: var(--text-tertiary); font-size: 14px;">▼</span>
                            </div>
                            <div style="flex: 1; background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center;">
                                <span>11:00 PM</span>
                                <span style="color: var(--text-tertiary); font-size: 14px;">▼</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Number of Workers Section -->
                    <div style="margin-bottom: 25px;">
                        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">Number of Workers Needed*</div>
                        <div style="display: flex; justify-content: space-between; align-items: center; background: white; border-radius: 12px; padding: 10px 16px; border: 1px solid rgba(0,0,0,0.1);">
                            <div style="width: 40px; height: 40px; border-radius: 20px; background: rgba(74, 111, 255, 0.1); display: flex; justify-content: center; align-items: center; font-size: 18px; color: var(--primary-color);">−</div>
                            <div style="font-size: 18px; font-weight: 600;">3</div>
                            <div style="width: 40px; height: 40px; border-radius: 20px; background: var(--primary-color); display: flex; justify-content: center; align-items: center; font-size: 18px; color: white;">+</div>
                        </div>
                    </div>
                </div>
                
                <div style="padding: 20px; margin-top: auto; display: flex; gap: 15px;">
                    <div style="flex: 1; padding: 16px; border-radius: 12px; border: 1px solid rgba(74, 111, 255, 0.3); text-align: center; color: var(--primary-color); font-weight: 500;" class="interactive-element">Save Draft</div>
                    <div class="button primary" style="flex: 1;">Continue</div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="display: flex; flex-direction: column; align-items: center;">
        <!-- Create New Job Screen (Step 2) -->
        <div style="margin-bottom: 10px; font-size: 18px; font-weight: 600; color: var(--primary-color);">Create New Job - Step 2</div>
        <div class="screen">
            <div class="screen-content">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>●●● ■</span>
                </div>
                <div style="padding: 20px; display: flex; align-items: center; gap: 10px;">
                    <div style="width: 40px; height: 40px; border-radius: 20px; background: rgba(74, 111, 255, 0.1); display: flex; justify-content: center; align-items: center; font-size: 18px;" class="interactive-element">
                        ←
                    </div>
                    <div style="font-size: 18px; font-weight: 600;">Requirements & Qualifications</div>
                </div>
                <div class="scrollable-content">
                    <!-- Job Creation Form Step 2 -->
                    <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
                        <div style="width: 30%;">
                            <div style="width: 60px; height: 60px; border-radius: 16px; background: rgba(74, 111, 255, 0.1); display: flex; justify-content: center; align-items: center; font-size: 30px;">
                                🔍
                            </div>
                        </div>
                        <div style="width: 70%; display: flex; flex-direction: column; justify-content: center;">
                            <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 5px;">Step 2 of 3</div>
                            <div style="height: 6px; background: #EEF2FF; border-radius: 3px; width: 100%;">
                                <div style="height: 6px; width: 66%; background: var(--primary-color); border-radius: 3px;"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Experience Level -->
                    <div style="margin-bottom: 25px;">
                        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">Experience Level*</div>
                        <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                            <div style="flex: 1; padding: 14px; border-radius: 12px; background: var(--primary-color); text-align: center; color: white; font-weight: 500;" class="interactive-element">Entry</div>
                            <div style="flex: 1; padding: 14px; border-radius: 12px; background: white; border: 1px solid rgba(0,0,0,0.1); text-align: center; color: var(--text-secondary);" class="interactive-element">Intermediate</div>
                            <div style="flex: 1; padding: 14px; border-radius: 12px; background: white; border: 1px solid rgba(0,0,0,0.1); text-align: center; color: var(--text-secondary);" class="interactive-element">Expert</div>
                        </div>
                    </div>
                    
                    <!-- Education Requirements -->
                    <div style="margin-bottom: 25px;">
                        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">Education</div>
                        <div style="background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center;" class="interactive-element">
                            <span>High School Diploma or Equivalent</span>
                            <span style="font-size: 14px; color: var(--text-tertiary);">▼</span>
                        </div>
                    </div>
                    
                    <!-- Certification Requirements -->
                    <div style="margin-bottom: 25px;">
                        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">Certifications (Optional)</div>
                        <div style="background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;" class="interactive-element">
                            <span>Food Handler's Certificate</span>
                            <span style="font-size: 14px; color: var(--text-tertiary);">✕</span>
                        </div>
                        <div style="background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center;" class="interactive-element">
                            <span style="color: var(--text-tertiary);">Add certification...</span>
                            <span style="color: var(--primary-color); font-size: 20px;">+</span>
                        </div>
                    </div>
                    
                    <!-- Physical Requirements -->
                    <div style="margin-bottom: 25px;">
                        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">Physical Requirements</div>
                        <div style="display: flex; flex-direction: column; gap: 15px;">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="width: 24px; height: 24px; border-radius: 6px; border: 2px solid var(--primary-color); background: var(--primary-color); display: flex; justify-content: center; align-items: center; color: white;" class="interactive-element">✓</div>
                                <div>Able to stand for extended periods</div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="width: 24px; height: 24px; border-radius: 6px; border: 2px solid var(--primary-color); background: var(--primary-color); display: flex; justify-content: center; align-items: center; color: white;" class="interactive-element">✓</div>
                                <div>Able to lift up to 25 pounds</div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="width: 24px; height: 24px; border-radius: 6px; border: 2px solid rgba(0,0,0,0.1); display: flex; justify-content: center; align-items: center;" class="interactive-element"></div>
                                <div>Able to perform repetitive tasks</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Dress Code -->
                    <div style="margin-bottom: 25px;">
                        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">Dress Code*</div>
                        <div style="background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1); height: 80px; font-size: 14px; color: var(--text-secondary);" class="interactive-element">
                            Black slacks, white button-up shirt, and closed-toe black shoes. Company will provide apron and name tag.
                        </div>
                    </div>
                    
                    <!-- Additional Notes -->
                    <div style="margin-bottom: 25px;">
                        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">Additional Notes (Optional)</div>
                        <div style="background: white; border-radius: 12px; padding: 16px; border: 1px solid rgba(0,0,0,0.1); height: 80px; font-size: 14px; color: var(--text-tertiary);" class="interactive-element">
                            Add any additional information about requirements or qualifications...
                        </div>
                    </div>
                </div>
                
                <div style="padding: 20px; margin-top: auto; display: flex; gap: 15px;">
                    <div style="flex: 1; padding: 16px; border-radius: 12px; border: 1px solid rgba(74, 111, 255, 0.3); text-align: center; color: var(--primary-color); font-weight: 500;" class="interactive-element">Back</div>
                    <div class="button primary" style="flex: 1;">Continue</div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="display: flex; flex-direction: column; align-items: center;">
        <!-- Create New Job Screen (Step 3) -->
        <div style="margin-bottom: 10px; font-size: 18px; font-weight: 600; color: var(--primary-color);">Create New Job - Step 3</div>
        <div class="screen">
            <div class="screen-content">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>●●● ■</span>
                </div>
                <div style="padding: 20px; display: flex; align-items: center; gap: 10px;">
                    <div style="width: 40px; height: 40px; border-radius: 20px; background: rgba(74, 111, 255, 0.1); display: flex; justify-content: center; align-items: center; font-size: 18px;" class="interactive-element">
                        ←
                    </div>
                    <div style="font-size: 18px; font-weight: 600;">Review & Post</div>
                </div>
                <div class="scrollable-content">
                    <!-- Job Creation Form Step 3 -->
                    <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
                        <div style="width: 30%;">
                            <div style="width: 60px; height: 60px; border-radius: 16px; background: rgba(74, 111, 255, 0.1); display: flex; justify-content: center; align-items: center; font-size: 30px;">
                                ✓
                            </div>
                        </div>
                        <div style="width: 70%; display: flex; flex-direction: column; justify-content: center;">
                            <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 5px;">Step 3 of 3</div>
                            <div style="height: 6px; background: #EEF2FF; border-radius: 3px; width: 100%;">
                                <div style="height: 6px; width: 100%; background: var(--primary-color); border-radius: 3px;"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Job Preview -->
                    <div style="margin-bottom: 25px;">
                        <div style="font-size: 18px; font-weight: 600; margin-bottom: 15px;">Job Listing Preview</div>
                        <div class="card" style="padding: 15px; margin: 0 0 20px 0;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <div style="font-size: 20px; font-weight: 600;">Event Staff</div>
                                <div class="tag">$22/hr</div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 5px; margin-bottom: 10px;">
                                <div style="font-size: 16px; color: var(--text-secondary);">Bay Area Events</div>
                                <div style="color: #FFD700; font-size: 14px;">★★★★</div>
                                <div style="font-size: 14px; color: var(--text-secondary);">(23)</div>
                            </div>
                            <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 15px;">
                                <div style="margin-bottom: 5px;">San Francisco, CA • Friday, July 28, 2023</div>
                                <div>5:00 PM - 11:00 PM (6 hours)</div>
                            </div>
                            <div style="display: flex; flex-wrap: wrap; gap: 8px; margin-bottom: 15px;">
                                <div class="tag">Customer Service</div>
                                <div class="tag">Hospitality</div>
                                <div class="tag">Communication</div>
                            </div>
                            <div style="font-size: 14px; line-height: 1.4; color: var(--text-secondary); margin-bottom: 10px;">
                                We are looking for reliable event staff to assist with a corporate event. Duties include guest check-in, serving food and drinks, and general event support.
                            </div>
                            <div style="font-size: 14px; font-weight: 500; color: var(--primary-color);" class="interactive-element">View Complete Details</div>
                        </div>
                    </div>
                    
                    <!-- Posting Options -->
                    <div style="margin-bottom: 25px;">
                        <div style="font-size: 16px; font-weight: 500; margin-bottom: 15px;">Posting Options</div>
                        <div style="display: flex; flex-direction: column; gap: 15px;">
                            <div style="display: flex; align-items: center; justify-content: space-between; padding: 15px; background: white; border-radius: 12px; border: 1px solid rgba(0,0,0,0.1);" class="interactive-element">
                                <div>
                                    <div style="font-weight: 500; margin-bottom: 3px;">Standard Posting</div>
                                    <div style="font-size: 14px; color: var(--text-secondary);">Regular visibility in search results</div>
                                </div>
                                <div style="width: 24px; height: 24px; border-radius: 50%; border: 2px solid var(--primary-color); position: relative;" class="interactive-element">
                                    <div style="width: 14px; height: 14px; border-radius: 50%; background: var(--primary-color); position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);"></div>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; justify-content: space-between; padding: 15px; background: rgba(74, 111, 255, 0.05); border-radius: 12px; border: 1px solid var(--primary-color);" class="interactive-element">
                                <div>
                                    <div style="font-weight: 500; margin-bottom: 3px; display: flex; align-items: center; gap: 5px;">
                                        Featured Posting <span style="background: var(--primary-color); color: white; font-size: 12px; padding: 3px 8px; border-radius: 10px;">RECOMMENDED</span>
                                    </div>
                                    <div style="font-size: 14px; color: var(--text-secondary);">Boosted visibility + Priority placement</div>
                                </div>
                                <div style="width: 24px; height: 24px; border-radius: 50%; border: 2px solid var(--primary-color); position: relative;" class="interactive-element">
                                    <div style="width: 14px; height: 14px; border-radius: 50%; background: var(--primary-color); position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);"></div>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; justify-content: space-between; padding: 15px; background: white; border-radius: 12px; border: 1px solid rgba(0,0,0,0.1);" class="interactive-element">
                                <div>
                                    <div style="font-weight: 500; margin-bottom: 3px;">Premium Posting</div>
                                    <div style="font-size: 14px; color: var(--text-secondary);">Top visibility + Targeted notifications</div>
                                </div>
                                <div style="width: 24px; height: 24px; border-radius: 50%; border: 2px solid rgba(0,0,0,0.1); position: relative;" class="interactive-element"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Posting Time -->
                    <div style="margin-bottom: 25px;">
                        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">When to Post*</div>
                        <div style="display: flex; gap: 10px;">
                            <div style="flex: 1; padding: 14px; border-radius: 12px; background: var(--primary-color); text-align: center; color: white; font-weight: 500;" class="interactive-element">Post Now</div>
                            <div style="flex: 1; padding: 14px; border-radius: 12px; background: white; border: 1px solid rgba(0,0,0,0.1); text-align: center; color: var(--text-secondary);" class="interactive-element">Schedule</div>
                        </div>
                    </div>
                    
                    <!-- Cost Summary -->
                    <div style="margin-bottom: 25px;">
                        <div style="font-size: 16px; font-weight: 500; margin-bottom: 10px;">Cost Summary</div>
                        <div style="background: white; border-radius: 12px; padding: 20px; border: 1px solid rgba(0,0,0,0.1);">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                <div style="font-size: 14px; color: var(--text-secondary);">Base Cost</div>
                                <div style="font-weight: 500;">$25.00</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                <div style="font-size: 14px; color: var(--text-secondary);">Featured Placement</div>
                                <div style="font-weight: 500;">$15.00</div>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
                                <div style="font-size: 14px; color: var(--text-secondary);">Service Fee</div>
                                <div style="font-weight: 500;">$3.00</div>
                            </div>
                            <div style="height: 1px; background: rgba(0,0,0,0.1); margin-bottom: 15px;"></div>
                            <div style="display: flex; justify-content: space-between;">
                                <div style="font-size: 16px; font-weight: 600;">Total</div>
                                <div style="font-size: 16px; font-weight: 600; color: var(--primary-color);">$43.00</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div style="padding: 20px; margin-top: auto; display: flex; gap: 15px;">
                    <div style="flex: 1; padding: 16px; border-radius: 12px; border: 1px solid rgba(74, 111, 255, 0.3); text-align: center; color: var(--primary-color); font-weight: 500;" class="interactive-element">Back</div>
                    <div class="button primary" style="flex: 1;">Post Job</div>
                </div>
            </div>
        </div>
    </div>
        
    <div style="display: flex; flex-direction: column; align-items: center;">
        <!-- Terms of Service Screen -->
        <div style="margin-bottom: 10px; font-size: 18px; font-weight: 600; color: var(--primary-color);">Terms of Service</div>
        <div class="screen">
            <div class="screen-content">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>●●● ■</span>
                </div>
                <div style="padding: 20px; display: flex; align-items: center; gap: 10px;">
                    <div style="width: 40px; height: 40px; border-radius: 20px; background: rgba(74, 111, 255, 0.1); display: flex; justify-content: center; align-items: center; font-size: 18px; cursor: pointer;">
                        ←
                    </div>
                    <div style="font-size: 18px; font-weight: 600;">Terms of Service</div>
                </div>
                <div class="scrollable-content">
                    <div style="font-size: 15px; color: var(--text-secondary); line-height: 1.7;">
                        <b>Welcome to WorkConnect!</b><br><br>
                        These Terms of Service ("Terms") govern your use of the WorkConnect mobile application and related services. By creating an account or using the app, you agree to these Terms.<br><br>
                        <b>1. Acceptance of Terms</b><br>
                        By accessing or using WorkConnect, you agree to be bound by these Terms and all applicable laws and regulations.<br><br>
                        <b>2. User Accounts</b><br>
                        You are responsible for maintaining the confidentiality of your account and password. You agree to provide accurate and complete information.<br><br>
                        <b>3. Prohibited Conduct</b><br>
                        You agree not to misuse the app, including but not limited to fraudulent activity, harassment, or violating any laws.<br><br>
                        <b>4. Termination</b><br>
                        We reserve the right to suspend or terminate your account at our discretion.<br><br>
                        <b>5. Changes to Terms</b><br>
                        We may update these Terms from time to time. Continued use of the app constitutes acceptance of the new Terms.<br><br>
                        <i>(This is placeholder text. Replace with your actual Terms of Service.)</i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div style="display: flex; flex-direction: column; align-items: center;">
        <!-- Privacy Policy Screen -->
        <div style="margin-bottom: 10px; font-size: 18px; font-weight: 600; color: var(--primary-color);">Privacy Policy</div>
        <div class="screen">
            <div class="screen-content">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>●●● ■</span>
                </div>
                <div style="padding: 20px; display: flex; align-items: center; gap: 10px;">
                    <div style="width: 40px; height: 40px; border-radius: 20px; background: rgba(74, 111, 255, 0.1); display: flex; justify-content: center; align-items: center; font-size: 18px; cursor: pointer;">
                        ←
                    </div>
                    <div style="font-size: 18px; font-weight: 600;">Privacy Policy</div>
                </div>
                <div class="scrollable-content">
                    <div style="font-size: 15px; color: var(--text-secondary); line-height: 1.7;">
                        <b>Your Privacy Matters</b><br><br>
                        This Privacy Policy explains how WorkConnect collects, uses, and protects your information.<br><br>
                        <b>1. Information We Collect</b><br>
                        We collect information you provide when you create an account, such as your name, email, and phone number.<br><br>
                        <b>2. How We Use Information</b><br>
                        We use your information to provide and improve our services, communicate with you, and ensure security.<br><br>
                        <b>3. Data Sharing</b><br>
                        We do not sell your personal information. We may share data with service providers as needed to operate the app.<br><br>
                        <b>4. Data Security</b><br>
                        We implement measures to protect your data, but cannot guarantee absolute security.<br><br>
                        <b>5. Changes to Policy</b><br>
                        We may update this Privacy Policy. Continued use of the app means you accept the new policy.<br><br>
                        <i>(This is placeholder text. Replace with your actual Privacy Policy.)</i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 