{"name": "mobile", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "prebuild": "expo prebuild", "android": "npm run prebuild && expo run:android", "ios": "npm run prebuild && expo run:ios", "web": "expo start --web", "test": "jest --config jest.config.js", "test:watch": "jest --watch --config jest.config.js", "test:coverage": "jest --coverage --config jest.config.js", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write 'src/**/*.{js,jsx,ts,tsx}'"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "^1.23.1", "@react-navigation/native": "^7.0.14", "expo": "~52.0.46", "expo-font": "~13.0.4", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-router": "~4.0.21", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.56.4", "react-native": "0.76.9", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-ui-lib": "^7.43.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.19.13"}, "devDependencies": {"@babel/core": "^7.25.2", "@eslint/js": "^9.26.0", "@react-native-community/cli": "^18.0.0", "@react-native-community/cli-platform-android": "17.0.0", "@react-native-community/cli-platform-ios": "17.0.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.4.3", "@types/jest": "^29.5.11", "@types/react": "~18.3.12", "@types/react-native": "^0.72.8", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.3", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-native": "^5.0.0", "jest": "^29.2.1", "jest-expo": "~52.0.6", "prettier": "^3.5.3", "react-test-renderer": "18.3.1", "typescript": "~5.3.3", "typescript-eslint": "^8.32.0"}, "private": true}