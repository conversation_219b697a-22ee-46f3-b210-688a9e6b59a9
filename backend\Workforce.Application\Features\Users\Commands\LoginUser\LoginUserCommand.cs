using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using MediatR;
using Workforce.Shared.DTOs;

namespace Workforce.Application.Features.Users.Commands.LoginUser
{
    /// <summary>
    /// Command for user login
    /// </summary>
    public class LoginUserCommand : IRequest<AuthenticatedUserDto>
    { 
        /// <summary>
        /// User's email address
        /// </summary>
        [Required]
        [EmailAddress]
        [MaxLength(255)]
        public string Email { get; init; } = string.Empty;

        /// <summary>
        /// User's password
        /// </summary>
        [Required]
        [MinLength(8)]
        [JsonIgnore]
        [System.Diagnostics.DebuggerHidden]
        public string Password { get; init; } = string.Empty;

        /// <summary>
        /// Optional: Remember me flag for extended session
        /// </summary>
        public bool RememberMe { get; init; } = false;

        /// <summary>
        /// Client IP address for audit logging
        /// </summary>
        public string? IpAddress { get; set; }

        /// <summary>
        /// User agent for audit logging
        /// </summary>
        public string? UserAgent { get; set; }
    }
}
