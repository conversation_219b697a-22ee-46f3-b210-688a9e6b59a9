import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { Colors } from '../../constants/Colors';

interface Role {
  id: string;
  title: string;
  description: string;
  icon: string;
  features: string[];
}

interface RoleCardProps {
  role: Role;
  isSelected: boolean;
  onSelect: (roleId: string) => void;
}

export const RoleCard: React.FC<RoleCardProps> = ({
  role,
  isSelected,
  onSelect,
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.roleCard,
        isSelected && styles.roleCardSelected,
      ]}
      onPress={() => onSelect(role.id)}
      activeOpacity={0.8}
      testID={`role-card-${role.id}`}
    >
      <View style={styles.roleCardContent}>
        {/* Icon and Title */}
        <View style={styles.roleHeader}>
          <View style={[
            styles.roleIconContainer,
            isSelected && styles.roleIconContainerSelected,
          ]}>
            <Text style={styles.roleIcon}>{role.icon}</Text>
          </View>
          <View style={styles.roleTitleContainer}>
            <Text style={[
              styles.roleTitle,
              isSelected && styles.roleTitleSelected,
            ]}>
              {role.title}
            </Text>
            <Text style={[
              styles.roleDescription,
              isSelected && styles.roleDescriptionSelected,
            ]}>
              {role.description}
            </Text>
          </View>
        </View>

        {/* Features List */}
        <View style={styles.featuresContainer}>
          {role.features.map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <Text style={[
                styles.featureIcon,
                isSelected && styles.featureIconSelected,
              ]}>
                ✓
              </Text>
              <Text style={[
                styles.featureText,
                isSelected && styles.featureTextSelected,
              ]}>
                {feature}
              </Text>
            </View>
          ))}
        </View>

        {/* Selection Indicator */}
        {isSelected && (
          <View style={styles.selectionIndicator}>
            <Text style={styles.selectionIcon}>✓</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  roleCard: {
    backgroundColor: Colors.light.card,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  roleCardSelected: {
    borderColor: Colors.light.primary,
    backgroundColor: 'rgba(74, 111, 255, 0.02)',
  },
  roleCardContent: {
    padding: 20,
    position: 'relative',
  },
  roleHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  roleIconContainer: {
    width: 60,
    height: 60,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  roleIconContainerSelected: {
    backgroundColor: 'rgba(74, 111, 255, 0.1)',
  },
  roleIcon: {
    fontSize: 28,
  },
  roleTitleContainer: {
    flex: 1,
  },
  roleTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 5,
  },
  roleTitleSelected: {
    color: Colors.light.primary,
  },
  roleDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  roleDescriptionSelected: {
    color: Colors.light.textSecondary,
  },
  featuresContainer: {
    gap: 8,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  featureIcon: {
    fontSize: 14,
    color: Colors.light.success,
    marginRight: 10,
    width: 16,
    textAlign: 'center',
  },
  featureIconSelected: {
    color: Colors.light.primary,
  },
  featureText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    flex: 1,
  },
  featureTextSelected: {
    color: Colors.light.text,
  },
  selectionIndicator: {
    position: 'absolute',
    top: 15,
    right: 15,
    width: 24,
    height: 24,
    backgroundColor: Colors.light.primary,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectionIcon: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
});
