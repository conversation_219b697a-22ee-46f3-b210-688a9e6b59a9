import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import WelcomeScreen from '../WelcomeScreen';

// Mock expo-router
const mockPush = jest.fn();
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: mockPush,
    back: jest.fn(),
    replace: jest.fn(),
  }),
}));

// Mock expo-linear-gradient
jest.mock('expo-linear-gradient', () => ({
  LinearGradient: ({ children, ...props }: any) => children,
}));

// Mock Colors
jest.mock('../../../constants/Colors', () => ({
  Colors: {
    light: {
      primary: '#007AFF',
      secondary: '#5856D6',
      background: '#FFFFFF',
      text: '#000000',
    },
  },
}));

describe('WelcomeScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    const { getByText } = render(<WelcomeScreen />);
    
    expect(getByText('HireNow')).toBeTruthy();
    expect(getByText('Find Flexible Work, Anytime')).toBeTruthy();
    expect(getByText('Connect with local opportunities that fit your schedule and skills')).toBeTruthy();
    expect(getByText('Get Started')).toBeTruthy();
    expect(getByText('Already have an account?')).toBeTruthy();
    expect(getByText('Sign In')).toBeTruthy();
  });

  it('displays the app logo and icon', () => {
    const { getByText } = render(<WelcomeScreen />);
    
    expect(getByText('HireNow')).toBeTruthy();
    expect(getByText('🤝')).toBeTruthy();
  });

  it('handles get started button press', () => {
    const { getByText } = render(<WelcomeScreen />);
    
    const getStartedButton = getByText('Get Started');
    fireEvent.press(getStartedButton);
    
    expect(mockPush).toHaveBeenCalledWith('/auth/register');
  });

  it('displays sign in text but does not handle press (commented out)', () => {
    const { getByText } = render(<WelcomeScreen />);
    
    const signInContainer = getByText('Already have an account?').parent;
    expect(signInContainer).toBeTruthy();
    
    // The sign in functionality is commented out in the component
    // so we just verify the text is displayed
    expect(getByText('Sign In')).toBeTruthy();
  });

  it('has proper styling structure', () => {
    const { getByText } = render(<WelcomeScreen />);
    
    // Verify main content elements are present
    expect(getByText('HireNow')).toBeTruthy();
    expect(getByText('🤝')).toBeTruthy();
    expect(getByText('Find Flexible Work, Anytime')).toBeTruthy();
    expect(getByText('Get Started')).toBeTruthy();
  });

  it('displays the correct subtitle text', () => {
    const { getByText } = render(<WelcomeScreen />);
    
    expect(getByText('Connect with local opportunities that fit your schedule and skills')).toBeTruthy();
  });

  it('has accessible button elements', () => {
    const { getByText } = render(<WelcomeScreen />);
    
    const getStartedButton = getByText('Get Started');
    expect(getStartedButton).toBeTruthy();
    
    // Verify button is pressable
    fireEvent.press(getStartedButton);
    expect(mockPush).toHaveBeenCalled();
  });

  it('renders with proper component hierarchy', () => {
    const { getByText } = render(<WelcomeScreen />);
    
    // Verify all main sections are rendered
    const appTitle = getByText('HireNow');
    const icon = getByText('🤝');
    const title = getByText('Find Flexible Work, Anytime');
    const subtitle = getByText('Connect with local opportunities that fit your schedule and skills');
    const button = getByText('Get Started');
    const signInText = getByText('Already have an account?');
    
    expect(appTitle).toBeTruthy();
    expect(icon).toBeTruthy();
    expect(title).toBeTruthy();
    expect(subtitle).toBeTruthy();
    expect(button).toBeTruthy();
    expect(signInText).toBeTruthy();
  });
});
