using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Workforce.Application.Options;

namespace Workforce.Application.Tests.Services
{
    public class RateLimitOptionsIntegrationTests
    {
        [Fact]
        public void RateLimitOptions_ShouldBindFromConfiguration()
        {
            // Arrange
            var configurationData = new Dictionary<string, string>
            {
                ["SecuritySettings:RateLimit:IpMaxAttempts"] = "15",
                ["SecuritySettings:RateLimit:EmailMaxAttempts"] = "8",
                ["SecuritySettings:RateLimit:IpWindowMinutes"] = "5",
                ["SecuritySettings:RateLimit:EmailWindowMinutes"] = "3",
                ["SecuritySettings:RateLimit:CleanupIntervalMinutes"] = "30",
                ["SecuritySettings:RateLimit:CleanupThresholdMinutes"] = "15",
                ["SecuritySettings:RateLimit:EnablePeriodicCleanup"] = "true"
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(configurationData!)
                .Build();

            var services = new ServiceCollection();
            services.Configure<RateLimitOptions>(options =>
                configuration.GetSection("SecuritySettings:RateLimit").Bind(options));

            var serviceProvider = services.BuildServiceProvider();

            // Act
            var options = serviceProvider.GetRequiredService<IOptions<RateLimitOptions>>().Value;

            // Assert
            options.IpMaxAttempts.Should().Be(15);
            options.EmailMaxAttempts.Should().Be(8);
            options.IpWindowMinutes.Should().Be(5);
            options.EmailWindowMinutes.Should().Be(3);
            options.CleanupIntervalMinutes.Should().Be(30);
            options.CleanupThresholdMinutes.Should().Be(15);
            options.EnablePeriodicCleanup.Should().BeTrue();
        }

        [Fact]
        public void RateLimitOptions_WithDefaultValues_ShouldUseDefaults()
        {
            // Arrange
            var configurationData = new Dictionary<string, string>();

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(configurationData!)
                .Build();

            var services = new ServiceCollection();
            services.Configure<RateLimitOptions>(options =>
                configuration.GetSection("SecuritySettings:RateLimit").Bind(options));

            var serviceProvider = services.BuildServiceProvider();

            // Act
            var options = serviceProvider.GetRequiredService<IOptions<RateLimitOptions>>().Value;

            // Assert - Should use default values from RateLimitOptions class
            options.IpMaxAttempts.Should().Be(10);
            options.EmailMaxAttempts.Should().Be(5);
            options.IpWindowMinutes.Should().Be(1);
            options.EmailWindowMinutes.Should().Be(1);
            options.CleanupIntervalMinutes.Should().Be(10);
            options.CleanupThresholdMinutes.Should().Be(5);
            options.EnablePeriodicCleanup.Should().BeTrue();
        }

        [Fact]
        public void RateLimitOptions_WithPartialConfiguration_ShouldUseDefaultsForMissingValues()
        {
            // Arrange
            var configurationData = new Dictionary<string, string>
            {
                ["SecuritySettings:RateLimit:IpMaxAttempts"] = "20",
                ["SecuritySettings:RateLimit:CleanupIntervalMinutes"] = "60",
                ["SecuritySettings:RateLimit:EnablePeriodicCleanup"] = "false"
                // Missing other values should use defaults
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(configurationData!)
                .Build();

            var services = new ServiceCollection();
            services.Configure<RateLimitOptions>(options =>
                configuration.GetSection("SecuritySettings:RateLimit").Bind(options));

            var serviceProvider = services.BuildServiceProvider();

            // Act
            var options = serviceProvider.GetRequiredService<IOptions<RateLimitOptions>>().Value;

            // Assert
            options.IpMaxAttempts.Should().Be(20); // Configured value
            options.EmailMaxAttempts.Should().Be(5); // Default value
            options.IpWindowMinutes.Should().Be(1); // Default value
            options.EmailWindowMinutes.Should().Be(1); // Default value
            options.CleanupIntervalMinutes.Should().Be(60); // Configured value
            options.CleanupThresholdMinutes.Should().Be(5); // Default value
            options.EnablePeriodicCleanup.Should().BeFalse(); // Configured value
        }

        [Fact]
        public void RateLimitOptions_WithStringValues_ShouldParseCorrectly()
        {
            // Arrange - Test with string values that need to be converted to appropriate types
            var configurationData = new Dictionary<string, string>
            {
                ["SecuritySettings:RateLimit:IpMaxAttempts"] = "25",
                ["SecuritySettings:RateLimit:EmailMaxAttempts"] = "12",
                ["SecuritySettings:RateLimit:IpWindowMinutes"] = "2",
                ["SecuritySettings:RateLimit:EmailWindowMinutes"] = "3",
                ["SecuritySettings:RateLimit:CleanupIntervalMinutes"] = "45",
                ["SecuritySettings:RateLimit:CleanupThresholdMinutes"] = "20",
                ["SecuritySettings:RateLimit:EnablePeriodicCleanup"] = "false"
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(configurationData!)
                .Build();

            var services = new ServiceCollection();
            services.Configure<RateLimitOptions>(options =>
                configuration.GetSection("SecuritySettings:RateLimit").Bind(options));

            var serviceProvider = services.BuildServiceProvider();

            // Act
            var options = serviceProvider.GetRequiredService<IOptions<RateLimitOptions>>().Value;

            // Assert - Verify that string values are correctly converted to appropriate types
            options.IpMaxAttempts.Should().Be(25);
            options.EmailMaxAttempts.Should().Be(12);
            options.IpWindowMinutes.Should().Be(2);
            options.EmailWindowMinutes.Should().Be(3);
            options.CleanupIntervalMinutes.Should().Be(45);
            options.CleanupThresholdMinutes.Should().Be(20);
            options.EnablePeriodicCleanup.Should().BeFalse();
        }
    }
}
