using FluentAssertions;
using Workforce.Application.Features.Users.Commands.LoginUser;
using Xunit;

namespace Workforce.Application.Tests.Features.Users.Commands.LoginUser
{
    public class LoginUserCommandValidatorTests
    {
        private readonly LoginUserCommandValidator _validator;

        public LoginUserCommandValidatorTests()
        {
            _validator = new LoginUserCommandValidator();
        }

        [Fact]
        public void Validate_ValidCommand_ShouldPass()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                Email = "<EMAIL>",
                Password = "Password123!"
            };

            // Act
            var result = _validator.Validate(command);

            // Assert
            result.IsValid.Should().BeTrue();
            result.Errors.Should().BeEmpty();
        }

        [Theory]
        [InlineData("")]
        [InlineData(" ")]
        public void Validate_EmptyEmail_ShouldFail(string email)
        {
            // Arrange
            var command = new LoginUserCommand
            {
                Email = email,
                Password = "Password123!"
            };

            // Act
            var result = _validator.Validate(command);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Errors.Should().Contain(x => x.PropertyName == nameof(LoginUserCommand.Email) && x.ErrorMessage == "Email is required");
        }

        [Fact]
        public void Validate_NullEmail_ShouldFail()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                Email = null!,
                Password = "Password123!"
            };

            // Act
            var result = _validator.Validate(command);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Errors.Should().Contain(x => x.PropertyName == nameof(LoginUserCommand.Email) && x.ErrorMessage == "Email is required");
        }

        [Theory]
        [InlineData("invalid-email")]
        [InlineData("test@")]
        [InlineData("@example.com")]
        [InlineData("test.example.com")]
        public void Validate_InvalidEmailFormat_ShouldFail(string email)
        {
            // Arrange
            var command = new LoginUserCommand
            {
                Email = email,
                Password = "Password123!"
            };

            // Act
            var result = _validator.Validate(command);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Errors.Should().Contain(x => x.PropertyName == nameof(LoginUserCommand.Email) && x.ErrorMessage == "Invalid email format");
        }

        [Fact]
        public void Validate_EmailTooLong_ShouldFail()
        {
            // Arrange
            var longEmail = new string('a', 250) + "@example.com"; // 261 characters
            var command = new LoginUserCommand
            {
                Email = longEmail,
                Password = "Password123!"
            };

            // Act
            var result = _validator.Validate(command);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Errors.Should().Contain(x => x.PropertyName == nameof(LoginUserCommand.Email) && x.ErrorMessage == "Email cannot exceed 255 characters");
        }

        [Theory]
        [InlineData("")]
        [InlineData(" ")]
        public void Validate_EmptyPassword_ShouldFail(string password)
        {
            // Arrange
            var command = new LoginUserCommand
            {
                Email = "<EMAIL>",
                Password = password
            };

            // Act
            var result = _validator.Validate(command);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Errors.Should().Contain(x => x.PropertyName == nameof(LoginUserCommand.Password) && x.ErrorMessage == "Password is required");
        }

        [Fact]
        public void Validate_NullPassword_ShouldFail()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                Email = "<EMAIL>",
                Password = null!
            };

            // Act
            var result = _validator.Validate(command);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Errors.Should().Contain(x => x.PropertyName == nameof(LoginUserCommand.Password) && x.ErrorMessage == "Password is required");
        }

        [Fact]
        public void Validate_PasswordTooShort_ShouldFail()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                Email = "<EMAIL>",
                Password = "1234567" // 7 characters
            };

            // Act
            var result = _validator.Validate(command);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Errors.Should().Contain(x => x.PropertyName == nameof(LoginUserCommand.Password) && x.ErrorMessage == "Password must be at least 8 characters long");
        }

        [Fact]
        public void Validate_PasswordTooLong_ShouldFail()
        {
            // Arrange
            var longPassword = new string('a', 101); // 101 characters
            var command = new LoginUserCommand
            {
                Email = "<EMAIL>",
                Password = longPassword
            };

            // Act
            var result = _validator.Validate(command);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Errors.Should().Contain(x => x.PropertyName == nameof(LoginUserCommand.Password) && x.ErrorMessage == "Password cannot exceed 100 characters");
        }

        [Theory]
        [InlineData("password123")]
        [InlineData("PASSWORD123")]
        [InlineData("Password")]
        [InlineData("12345678")]
        public void Validate_ValidPasswordFormats_ShouldPass(string password)
        {
            // Arrange
            var command = new LoginUserCommand
            {
                Email = "<EMAIL>",
                Password = password
            };

            // Act
            var result = _validator.Validate(command);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void Validate_MultipleErrors_ShouldReturnAllErrors()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                Email = "invalid-email",
                Password = "123" // Too short
            };

            // Act
            var result = _validator.Validate(command);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Errors.Should().HaveCount(2);
            result.Errors.Should().Contain(x => x.PropertyName == nameof(LoginUserCommand.Email));
            result.Errors.Should().Contain(x => x.PropertyName == nameof(LoginUserCommand.Password));
        }

        [Fact]
        public void Validate_CaseInsensitiveEmail_ShouldPass()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                Email = "<EMAIL>",
                Password = "Password123!"
            };

            // Act
            var result = _validator.Validate(command);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void Validate_EmailWithSpecialCharacters_ShouldPass()
        {
            // Arrange
            var command = new LoginUserCommand
            {
                Email = "<EMAIL>",
                Password = "Password123!"
            };

            // Act
            var result = _validator.Validate(command);

            // Assert
            result.IsValid.Should().BeTrue();
        }
    }
}
