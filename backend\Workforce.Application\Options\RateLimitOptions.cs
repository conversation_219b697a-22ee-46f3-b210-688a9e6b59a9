namespace Workforce.Application.Options
{
    /// <summary>
    /// Configuration options for rate limiting settings
    /// </summary>
    public class RateLimitOptions
    {
        /// <summary>
        /// Maximum number of IP login attempts within the time window
        /// </summary>
        public int IpMaxAttempts { get; set; } = 10;

        /// <summary>
        /// Time window in minutes for IP rate limiting
        /// </summary>
        public int IpWindowMinutes { get; set; } = 1;

        /// <summary>
        /// Maximum number of email login attempts within the time window
        /// </summary>
        public int EmailMaxAttempts { get; set; } = 5;

        /// <summary>
        /// Time window in minutes for email rate limiting
        /// </summary>
        public int EmailWindowMinutes { get; set; } = 1;

        /// <summary>
        /// Interval in minutes for periodic cleanup of unused semaphores
        /// </summary>
        public int CleanupIntervalMinutes { get; set; } = 10;

        /// <summary>
        /// Threshold in minutes - semaphores unused for this duration will be cleaned up
        /// </summary>
        public int CleanupThresholdMinutes { get; set; } = 5;

        /// <summary>
        /// Whether to enable periodic cleanup of unused semaphores
        /// </summary>
        public bool EnablePeriodicCleanup { get; set; } = true;
    }
}
