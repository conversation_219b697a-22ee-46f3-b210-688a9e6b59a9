@Workforce.API_HostAddress = http://localhost:5000

GET {{Workforce.API_HostAddress}}/api/v1/auth/health
Accept: application/json

###

POST {{Workforce.API_HostAddress}}/api/v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "TestPassword123!",
  "confirmPassword": "TestPassword123!",
  "firstName": "Test",
  "lastName": "User",
  "phoneNumber": "+**********",
  "role": "JobSeeker"
}

###

POST {{Workforce.API_HostAddress}}/api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "TestPassword123!",
  "rememberMe": false
}

###

# Test login with invalid credentials
POST {{Workforce.API_HostAddress}}/api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "WrongPassword",
  "rememberMe": false
}

###

# Test login with non-existent user
POST {{Workforce.API_HostAddress}}/api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "TestPassword123!",
  "rememberMe": false
}

###
