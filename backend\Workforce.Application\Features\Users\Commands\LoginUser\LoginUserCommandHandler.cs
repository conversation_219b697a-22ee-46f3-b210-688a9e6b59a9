using AutoMapper;
using MediatR;
using Microsoft.Extensions.Options;
using Workforce.Application.Options;
using Workforce.Application.Services;
using Workforce.Domain.Repositories;
using Workforce.Shared.DTOs;

namespace Workforce.Application.Features.Users.Commands.LoginUser
{
    /// <summary>
    /// Handler for LoginUserCommand
    /// </summary>
    public class LoginUserCommandHandler : IRequestHandler<LoginUserCommand, AuthenticatedUserDto>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IPasswordHashingService _passwordHashingService;
        private readonly IJwtTokenService _jwtTokenService;
        private readonly IAuditLoggingService _auditLoggingService;
        private readonly IRateLimitingService _rateLimitingService;
        private readonly AccountLockoutOptions _accountLockoutOptions;

        public LoginUserCommandHandler(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            IPasswordHashingService passwordHashingService,
            IJwtTokenService jwtTokenService,
            IAuditLoggingService auditLoggingService,
            IRateLimitingService rateLimitingService,
            IOptions<AccountLockoutOptions> accountLockoutOptions)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _passwordHashingService = passwordHashingService;
            _jwtTokenService = jwtTokenService;
            _auditLoggingService = auditLoggingService;
            _rateLimitingService = rateLimitingService;
            _accountLockoutOptions = accountLockoutOptions.Value;
        }

        /// <summary>
        /// Handles the user login command
        /// </summary>
        /// <param name="request">The login command</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Authenticated user DTO with tokens</returns>
        public async Task<AuthenticatedUserDto> Handle(LoginUserCommand request, CancellationToken cancellationToken)
        {
            var email = request.Email.ToLowerInvariant().Trim();

            // Record login attempt for rate limiting
            if (!string.IsNullOrWhiteSpace(request.IpAddress))
                await _rateLimitingService.RecordIpLoginAttemptAsync(request.IpAddress);
            await _rateLimitingService.RecordEmailLoginAttemptAsync(email);

            // Check rate limits
            await CheckRateLimitsAsync(request.IpAddress, email, request.UserAgent);

            // Get user by email
            var user = await _unitOfWork.Users.GetByEmailAsync(email);
            if (user == null)
            {
                // Perform dummy password verification to prevent timing attacks
                _passwordHashingService.VerifyPassword("dummy_hash", request.Password);

                await _auditLoggingService.LogFailedLoginAsync(email, "User not found", request.IpAddress, request.UserAgent);
                throw new UnauthorizedAccessException("Invalid email or password");
            }

            // Check if account is locked out
            if (user.IsLockedOut(DateTime.UtcNow))
            {
                await _auditLoggingService.LogFailedLoginAsync(email, "Account locked out", request.IpAddress, request.UserAgent);
                throw new UnauthorizedAccessException("Account is temporarily locked due to multiple failed login attempts");
            }

            // Check if account is active
            if (!user.IsActive)
            {
                await _auditLoggingService.LogFailedLoginAsync(email, "Account inactive", request.IpAddress, request.UserAgent);
                throw new UnauthorizedAccessException("Account is inactive");
            }

            // Verify password
            var isPasswordValid = _passwordHashingService.VerifyPassword(user.PasswordHash, request.Password);
            if (!isPasswordValid)
            {
                await HandleFailedLoginAsync(user, email, request.IpAddress, request.UserAgent);
                throw new UnauthorizedAccessException("Invalid email or password");
            }

            // Reset failed login attempts on successful login
            if (user.FailedLoginAttempts > 0)
            {
                await _unitOfWork.Users.ResetFailedLoginAttemptsAsync(user.UserId);
            }

            // Update last login timestamp
            await _unitOfWork.Users.UpdateLastLoginAsync(user.UserId, DateTime.UtcNow);

            // Generate JWT tokens
            string? accessToken;
            string? refreshToken;
            int expirationSeconds;

            try
            {
                accessToken = _jwtTokenService.GenerateAccessToken(user);
                refreshToken = _jwtTokenService.GenerateRefreshToken();
                expirationSeconds = _jwtTokenService.GetTokenExpirationSeconds();
            }
            catch (Exception ex)
            {
                await _auditLoggingService.LogFailedLoginAsync(email, "Token generation failed", request.IpAddress, request.UserAgent);
                throw new InvalidOperationException("Login failed due to system error", ex);
            }

            // Map user to DTO
            var userProfileDto = _mapper.Map<UserProfileDto>(user);

            // Log successful login
            await _auditLoggingService.LogSuccessfulLoginAsync(user.UserId, email, request.IpAddress, request.UserAgent);

            // Create the authenticated user response
            var authenticatedUserDto = new AuthenticatedUserDto
            {
                User = userProfileDto,
                AccessToken = accessToken,
                RefreshToken = refreshToken,
                TokenType = "Bearer",
                ExpiresIn = expirationSeconds,
                ExpiresAt = DateTime.UtcNow.AddSeconds(expirationSeconds)
            };

            return authenticatedUserDto;
        }

        /// <summary>
        /// Checks rate limits for IP and email
        /// </summary>
        private async Task CheckRateLimitsAsync(string? ipAddress, string email, string? userAgent)
        {
            // Check IP rate limit
            if (!string.IsNullOrWhiteSpace(ipAddress))
            {
                var isIpLimited = await _rateLimitingService.IsIpRateLimitExceededAsync(ipAddress);
                if (isIpLimited)
                {
                    var resetTime = await _rateLimitingService.GetIpRateLimitResetTimeAsync(ipAddress);
                    await _auditLoggingService.LogRateLimitExceededAsync(ipAddress, "IP", ipAddress, userAgent);
                    throw new InvalidOperationException($"Too many login attempts from this IP address. Try again in {resetTime.TotalMinutes:F0} minutes.");
                }
            }

            // Check email rate limit
            var isEmailLimited = await _rateLimitingService.IsEmailRateLimitExceededAsync(email);
            if (isEmailLimited)
            {
                var resetTime = await _rateLimitingService.GetEmailRateLimitResetTimeAsync(email);
                await _auditLoggingService.LogRateLimitExceededAsync(email, "Email", ipAddress, userAgent);
                throw new InvalidOperationException($"Too many login attempts for this email. Try again in {resetTime.TotalMinutes:F0} minutes.");
            }
        }

        /// <summary>
        /// Handles failed login attempts and potential account lockout
        /// </summary>
        private async Task HandleFailedLoginAsync(Domain.Entities.User user, string email, string? ipAddress, string? userAgent)
        {
            // Atomically increment failed login attempts and get the new count
            var newFailedAttempts = await _unitOfWork.Users.IncrementFailedLoginAttemptsAndGetCountAsync(user.UserId);

            // Check if account should be locked out
            if (newFailedAttempts >= _accountLockoutOptions.MaxFailedAttempts)
            {
                var lockoutEnd = DateTime.UtcNow.AddMinutes(_accountLockoutOptions.LockoutDurationMinutes);
                await _unitOfWork.Users.LockoutUserAsync(user.UserId, lockoutEnd);
                await _auditLoggingService.LogAccountLockoutAsync(user.UserId, email, lockoutEnd, ipAddress, userAgent);
                await _auditLoggingService.LogFailedLoginAsync(email, "Invalid password - Account locked", ipAddress, userAgent);
            }
            else
            {
                await _auditLoggingService.LogFailedLoginAsync(email, "Invalid password", ipAddress, userAgent);
            }
        }
    }
}
