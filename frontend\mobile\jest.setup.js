// Include any global setup for the Jest testing environment
const { jest } = require('@jest/globals');
require('@testing-library/jest-native/extend-expect');

// Mock the Expo modules that might cause issues in the testing environment
jest.mock('expo-font', () => ({}));
jest.mock('expo-asset', () => ({}));
jest.mock('expo-linear-gradient', () => ({
  LinearGradient: ({ children, ...props }) => children,
}));

// Mock expo-router
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
    replace: jest.fn(),
  }),
  useLocalSearchParams: () => ({}),
}));

// Mock the Animated module instead of NativeAnimatedHelper
jest.mock('react-native/Libraries/Animated/Animated', () => ({
  timing: () => ({
    start: jest.fn(),
  }),
  loop: jest.fn(),
  Value: jest.fn(() => ({
    interpolate: jest.fn(),
  })),
}));

// Mock Alert separately to avoid issues with React Native modules
jest.mock('react-native/Libraries/Alert/Alert', () => ({
  alert: jest.fn(),
}));

// Mock React Native components that might cause issues
jest.mock('react-native-vector-icons', () => 'Icon');

// Mock custom components with correct paths
jest.mock('./src/components/common/CustomTextInput', () => ({
  CustomTextInput: ({ children, ...props }) => children,
}));

jest.mock('./src/components/common/CustomButton', () => ({
  CustomButton: ({ children, title, ...props }) => title,
}));

jest.mock('./src/components/common/PhoneNumberInput', () => ({
  PhoneNumberInput: ({ children, ...props }) => children,
}));

jest.mock('./src/components/common/CheckboxInput', () => ({
  CheckboxInput: ({ children, ...props }) => children,
}));

jest.mock('./src/components/auth/PasswordStrengthIndicator', () => ({
  PasswordStrengthIndicator: ({ children, ...props }) => children,
}));

jest.mock('./src/components/auth/RoleCard', () => ({
  RoleCard: ({ role, ...props }) => role.title,
}));

jest.mock('./src/components/auth/OTPInput', () => ({
  OTPInput: ({ children, ...props }) => children,
}));

jest.mock('./src/components/profile/ProfileImagePicker', () => ({
  ProfileImagePicker: ({ children, ...props }) => children,
}));

jest.mock('./src/components/profile/SkillsSelector', () => ({
  SkillsSelector: ({ children, ...props }) => children,
}));

jest.mock('./src/components/profile/LocationSelector', () => ({
  LocationSelector: ({ children, ...props }) => children,
}));

// Mock constants
jest.mock('./src/constants/Colors', () => ({
  Colors: {
    light: {
      primary: '#007AFF',
      secondary: '#5856D6',
      background: '#FFFFFF',
      text: '#000000',
    },
  },
}));

// This enables proper error messages for React tests
globalThis.ErrorUtils = {
  setGlobalHandler: jest.fn(),
};
