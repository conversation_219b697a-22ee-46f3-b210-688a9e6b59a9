import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import RegisterScreen from '../RegisterScreen';

// Mock expo-router
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

// Mock expo-linear-gradient
jest.mock('expo-linear-gradient', () => ({
  LinearGradient: ({ children, ...props }: any) => children,
}));

describe('RegisterScreen', () => {
  it('renders correctly', () => {
    const { getByText, getByPlaceholderText } = render(<RegisterScreen />);
    
    expect(getByText('Create Account')).toBeTruthy();
    expect(getByText('Sign up to find flexible work opportunities')).toBeTruthy();
    expect(getByPlaceholderText('John Doe')).toBeTruthy();
    expect(getByPlaceholderText('<EMAIL>')).toBeTruthy();
  });

  it('shows validation errors for empty required fields', async () => {
    const { getByText, getByTestId } = render(<RegisterScreen />);
    
    // Try to submit without filling required fields
    const submitButton = getByText('Create Account');
    fireEvent.press(submitButton);

    await waitFor(() => {
      expect(getByText('Full name is required')).toBeTruthy();
      expect(getByText('Email is required')).toBeTruthy();
      expect(getByText('Phone number is required')).toBeTruthy();
      expect(getByText('Password is required')).toBeTruthy();
    });
  });

  it('validates email format', async () => {
    const { getByPlaceholderText, getByText } = render(<RegisterScreen />);
    
    const emailInput = getByPlaceholderText('<EMAIL>');
    fireEvent.changeText(emailInput, 'invalid-email');
    fireEvent(emailInput, 'blur');

    await waitFor(() => {
      expect(getByText('Please enter a valid email address')).toBeTruthy();
    });
  });

  it('validates password strength', async () => {
    const { getByPlaceholderText, getByText } = render(<RegisterScreen />);
    
    const passwordInput = getByPlaceholderText('••••••••');
    fireEvent.changeText(passwordInput, 'weak');

    await waitFor(() => {
      expect(getByText('Password must be at least 8 characters long')).toBeTruthy();
    });
  });

  it('validates password confirmation', async () => {
    const { getAllByPlaceholderText, getByText } = render(<RegisterScreen />);
    
    const passwordInputs = getAllByPlaceholderText('••••••••');
    const passwordInput = passwordInputs[0];
    const confirmPasswordInput = passwordInputs[1];

    fireEvent.changeText(passwordInput, 'Password123!');
    fireEvent.changeText(confirmPasswordInput, 'DifferentPassword');
    fireEvent(confirmPasswordInput, 'blur');

    await waitFor(() => {
      expect(getByText('Passwords do not match')).toBeTruthy();
    });
  });

  it('requires terms agreement', async () => {
    const { getByText } = render(<RegisterScreen />);
    
    const submitButton = getByText('Create Account');
    fireEvent.press(submitButton);

    await waitFor(() => {
      expect(getByText('You must agree to the terms and conditions')).toBeTruthy();
    });
  });

  it('shows password strength indicator when password is entered', async () => {
    const { getByPlaceholderText, getByText } = render(<RegisterScreen />);
    
    const passwordInput = getByPlaceholderText('••••••••');
    fireEvent.changeText(passwordInput, 'Password123!');

    await waitFor(() => {
      expect(getByText('Strong')).toBeTruthy();
    });
  });
});
