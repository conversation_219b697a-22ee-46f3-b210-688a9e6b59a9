import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { Colors } from '../../constants/Colors';

interface CheckboxInputProps {
  label: string;
  value: boolean;
  onValueChange: (value: boolean) => void;
  error?: string;
}

export const CheckboxInput: React.FC<CheckboxInputProps> = ({
  label,
  value,
  onValueChange,
  error,
}) => {
  const handlePress = () => {
    onValueChange(!value);
  };

  const renderLabel = () => {
    // Split the label to handle links
    const parts = label.split(/(Terms of Service|Privacy Policy)/);
    
    return (
      <Text style={styles.label}>
        {parts.map((part, index) => {
          if (part === 'Terms of Service' || part === 'Privacy Policy') {
            return (
              <Text key={index} style={styles.linkText}>
                {part}
              </Text>
            );
          }
          return part;
        })}
      </Text>
    );
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.checkboxContainer}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <View style={[styles.checkbox, value && styles.checkboxChecked]}>
          {value && <Text style={styles.checkmark}>✓</Text>}
        </View>
        <View style={styles.labelContainer}>
          {renderLabel()}
        </View>
      </TouchableOpacity>
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 25,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 10,
  },
  checkbox: {
    width: 22,
    height: 22,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.card,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 2,
  },
  checkboxChecked: {
    backgroundColor: Colors.light.primary,
  },
  checkmark: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  labelContainer: {
    flex: 1,
  },
  label: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  linkText: {
    color: Colors.light.primary,
    textDecorationLine: 'underline',
  },
  errorText: {
    fontSize: 12,
    color: Colors.light.danger,
    marginTop: 4,
    marginLeft: 32,
  },
});
