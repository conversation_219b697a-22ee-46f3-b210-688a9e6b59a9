using System.ComponentModel.DataAnnotations;
using FluentAssertions;
using Workforce.Domain.Entities;
using Workforce.Domain.Enums;

namespace Workforce.Domain.Tests.Entities
{
    /// <summary>
    /// Unit tests for the User entity
    /// </summary>
    public class UserTests
    {
        #region Constructor and Property Tests

        [Fact]
        public void User_DefaultConstructor_ShouldSetDefaultValues()
        {
            // Act
            var user = new User
            {
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                PhoneNumber = "+1234567890"
            };

            // Assert
            user.UserId.Should().Be(Guid.Empty); // UserId defaults to empty GUID
            user.FirstName.Should().Be(string.Empty);
            user.LastName.Should().Be(string.Empty);
            user.IsVerified.Should().BeFalse();
            user.IsActive.Should().BeTrue();
            user.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            user.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            user.DeletedAt.Should().BeNull();
            user.ProfilePhotoUrl.Should().BeNull();
            user.DeviceToken.Should().BeNull();
            user.FcmToken.Should().BeNull();
            user.Preferences.Should().BeNull();
            user.AppSettings.Should().BeNull();
            user.LastLogin.Should().BeNull();
            user.LastSeenOnline.Should().BeNull();
        }

        [Fact]
        public void User_WithAllProperties_ShouldSetCorrectly()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var email = "<EMAIL>";
            var passwordHash = "hashedPassword123";
            var phoneNumber = "+1234567890";
            var firstName = "John";
            var lastName = "Doe";
            var role = UserRole.JobSeeker;
            var profilePhotoUrl = "https://example.com/photo.jpg";
            var deviceToken = "device_token_123";
            var fcmToken = "fcm_token_123";
            var preferences = "{\"theme\":\"dark\"}";
            var appSettings = "{\"notifications\":true}";
            var createdAt = DateTime.UtcNow.AddDays(-1);
            var updatedAt = DateTime.UtcNow;
            var lastLogin = DateTime.UtcNow.AddHours(-2);
            var lastSeenOnline = DateTime.UtcNow.AddMinutes(-30);

            // Act
            var user = new User
            {
                UserId = userId,
                Email = email,
                PasswordHash = passwordHash,
                PhoneNumber = phoneNumber,
                FirstName = firstName,
                LastName = lastName,
                Role = role,
                ProfilePhotoUrl = profilePhotoUrl,
                IsVerified = true,
                IsActive = true,
                DeviceToken = deviceToken,
                FcmToken = fcmToken,
                Preferences = preferences,
                AppSettings = appSettings,
                CreatedAt = createdAt,
                UpdatedAt = updatedAt,
                LastLogin = lastLogin,
                LastSeenOnline = lastSeenOnline
            };

            // Assert
            user.UserId.Should().Be(userId);
            user.Email.Should().Be(email);
            user.PasswordHash.Should().Be(passwordHash);
            user.PhoneNumber.Should().Be(phoneNumber);
            user.FirstName.Should().Be(firstName);
            user.LastName.Should().Be(lastName);
            user.Role.Should().Be(role);
            user.ProfilePhotoUrl.Should().Be(profilePhotoUrl);
            user.IsVerified.Should().BeTrue();
            user.IsActive.Should().BeTrue();
            user.DeviceToken.Should().Be(deviceToken);
            user.FcmToken.Should().Be(fcmToken);
            user.Preferences.Should().Be(preferences);
            user.AppSettings.Should().Be(appSettings);
            user.CreatedAt.Should().Be(createdAt);
            user.UpdatedAt.Should().Be(updatedAt);
            user.LastLogin.Should().Be(lastLogin);
            user.LastSeenOnline.Should().Be(lastSeenOnline);
            user.DeletedAt.Should().BeNull();
        }

        #endregion

        #region Computed Properties Tests

        [Theory]
        [InlineData("John", "Doe", "John Doe")]
        [InlineData("Jane", "Smith", "Jane Smith")]
        [InlineData("", "Doe", "Doe")]
        [InlineData("John", "", "John")]
        [InlineData("", "", "")]
        [InlineData("  John  ", "  Doe  ", "John     Doe")]
        public void FullName_ShouldReturnCorrectFormat(string firstName, string lastName, string expectedFullName)
        {
            // Arrange
            var user = new User
            {
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                PhoneNumber = "+1234567890",
                FirstName = firstName,
                LastName = lastName
            };

            // Act & Assert
            user.FullName.Should().Be(expectedFullName);
        }

        [Fact]
        public void IsDeleted_WhenDeletedAtIsNull_ShouldReturnFalse()
        {
            // Arrange
            var user = new User
            {
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                PhoneNumber = "+1234567890",
                DeletedAt = null
            };

            // Act & Assert
            user.IsDeleted.Should().BeFalse();
        }

        [Fact]
        public void IsDeleted_WhenDeletedAtHasValue_ShouldReturnTrue()
        {
            // Arrange
            var user = new User
            {
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                PhoneNumber = "+1234567890",
                DeletedAt = DateTime.UtcNow
            };

            // Act & Assert
            user.IsDeleted.Should().BeTrue();
        }

        #endregion

        #region Role Tests

        [Theory]
        [InlineData(UserRole.JobSeeker)]
        [InlineData(UserRole.Employer)]
        public void Role_ShouldAcceptValidUserRoles(UserRole role)
        {
            // Arrange & Act
            var user = new User
            {
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                PhoneNumber = "+1234567890",
                Role = role
            };

            // Assert
            user.Role.Should().Be(role);
        }

        #endregion

        #region Validation Scenarios Tests

        [Fact]
        public void User_WithValidJobSeekerData_ShouldCreateSuccessfully()
        {
            // Arrange & Act
            var user = new User
            {
                UserId = Guid.NewGuid(),
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                PhoneNumber = "+1234567890",
                FirstName = "John",
                LastName = "Doe",
                Role = UserRole.JobSeeker,
                IsVerified = false,
                IsActive = true
            };

            // Assert
            user.Should().NotBeNull();
            user.Role.Should().Be(UserRole.JobSeeker);
            user.Email.Should().Be("<EMAIL>");
            user.IsActive.Should().BeTrue();
            user.IsVerified.Should().BeFalse();
        }

        [Fact]
        public void User_WithValidEmployerData_ShouldCreateSuccessfully()
        {
            // Arrange & Act
            var user = new User
            {
                UserId = Guid.NewGuid(),
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                PhoneNumber = "+1987654321",
                FirstName = "Jane",
                LastName = "Smith",
                Role = UserRole.Employer,
                IsVerified = true,
                IsActive = true
            };

            // Assert
            user.Should().NotBeNull();
            user.Role.Should().Be(UserRole.Employer);
            user.Email.Should().Be("<EMAIL>");
            user.IsActive.Should().BeTrue();
            user.IsVerified.Should().BeTrue();
        }

        #endregion

        #region Soft Delete Tests

        [Fact]
        public void User_WhenSoftDeleted_ShouldSetDeletedAtAndIsDeleted()
        {
            // Arrange
            var user = new User
            {
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                PhoneNumber = "+1234567890",
                DeletedAt = null
            };

            var deletedAt = DateTime.UtcNow;

            // Act
            user.DeletedAt = deletedAt;

            // Assert
            user.DeletedAt.Should().Be(deletedAt);
            user.IsDeleted.Should().BeTrue();
        }

        [Fact]
        public void User_WhenRestored_ShouldClearDeletedAtAndIsDeleted()
        {
            // Arrange
            var user = new User
            {
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                PhoneNumber = "+1234567890",
                DeletedAt = DateTime.UtcNow
            };

            // Act
            user.DeletedAt = null;

            // Assert
            user.DeletedAt.Should().BeNull();
            user.IsDeleted.Should().BeFalse();
        }

        #endregion

        #region Timestamp Tests

        [Fact]
        public void User_CreatedAt_ShouldBeSetToCurrentUtcTime()
        {
            // Arrange
            var beforeCreation = DateTime.UtcNow;

            // Act
            var user = new User
            {
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                PhoneNumber = "+1234567890"
            };

            var afterCreation = DateTime.UtcNow;

            // Assert
            user.CreatedAt.Should().BeOnOrAfter(beforeCreation);
            user.CreatedAt.Should().BeOnOrBefore(afterCreation);
        }

        [Fact]
        public void User_UpdatedAt_ShouldBeSetToCurrentUtcTime()
        {
            // Arrange
            var beforeCreation = DateTime.UtcNow;

            // Act
            var user = new User
            {
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                PhoneNumber = "+1234567890"
            };

            var afterCreation = DateTime.UtcNow;

            // Assert
            user.UpdatedAt.Should().BeOnOrAfter(beforeCreation);
            user.UpdatedAt.Should().BeOnOrBefore(afterCreation);
        }

        #endregion

        #region Nullable Properties Tests

        [Fact]
        public void User_OptionalProperties_ShouldAcceptNullValues()
        {
            // Arrange & Act
            var user = new User
            {
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                PhoneNumber = "+1234567890",
                ProfilePhotoUrl = null,
                DeviceToken = null,
                FcmToken = null,
                Preferences = null,
                AppSettings = null,
                LastLogin = null,
                LastSeenOnline = null,
                DeletedAt = null
            };

            // Assert
            user.ProfilePhotoUrl.Should().BeNull();
            user.DeviceToken.Should().BeNull();
            user.FcmToken.Should().BeNull();
            user.Preferences.Should().BeNull();
            user.AppSettings.Should().BeNull();
            user.LastLogin.Should().BeNull();
            user.LastSeenOnline.Should().BeNull();
            user.DeletedAt.Should().BeNull();
        }

        [Fact]
        public void User_OptionalProperties_ShouldAcceptValidValues()
        {
            // Arrange
            var profilePhotoUrl = "https://example.com/photo.jpg";
            var deviceToken = "device_token_123";
            var fcmToken = "fcm_token_123";
            var preferences = "{\"theme\":\"dark\",\"language\":\"en\"}";
            var appSettings = "{\"notifications\":true,\"sound\":false}";
            var lastLogin = DateTime.UtcNow.AddHours(-1);
            var lastSeenOnline = DateTime.UtcNow.AddMinutes(-15);

            // Act
            var user = new User
            {
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                PhoneNumber = "+1234567890",
                ProfilePhotoUrl = profilePhotoUrl,
                DeviceToken = deviceToken,
                FcmToken = fcmToken,
                Preferences = preferences,
                AppSettings = appSettings,
                LastLogin = lastLogin,
                LastSeenOnline = lastSeenOnline
            };

            // Assert
            user.ProfilePhotoUrl.Should().Be(profilePhotoUrl);
            user.DeviceToken.Should().Be(deviceToken);
            user.FcmToken.Should().Be(fcmToken);
            user.Preferences.Should().Be(preferences);
            user.AppSettings.Should().Be(appSettings);
            user.LastLogin.Should().Be(lastLogin);
            user.LastSeenOnline.Should().Be(lastSeenOnline);
        }

        #endregion

        #region Business Logic Tests

        [Fact]
        public void User_NewUser_ShouldBeActiveAndUnverified()
        {
            // Arrange & Act
            var user = new User
            {
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                PhoneNumber = "+1234567890",
                FirstName = "New",
                LastName = "User",
                Role = UserRole.JobSeeker
            };

            // Assert
            user.IsActive.Should().BeTrue("new users should be active by default");
            user.IsVerified.Should().BeFalse("new users should be unverified by default");
            user.IsDeleted.Should().BeFalse("new users should not be deleted");
        }

        [Fact]
        public void User_VerifiedUser_ShouldMaintainVerificationStatus()
        {
            // Arrange
            var user = new User
            {
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                PhoneNumber = "+1234567890",
                IsVerified = false
            };

            // Act
            user.IsVerified = true;

            // Assert
            user.IsVerified.Should().BeTrue();
        }

        [Fact]
        public void User_DeactivatedUser_ShouldMaintainActiveStatus()
        {
            // Arrange
            var user = new User
            {
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                PhoneNumber = "+1234567890",
                IsActive = true
            };

            // Act
            user.IsActive = false;

            // Assert
            user.IsActive.Should().BeFalse();
        }

        #endregion

        #region Edge Cases Tests

        [Fact]
        public void User_WithEmptyGuid_ShouldAcceptEmptyGuid()
        {
            // Arrange & Act
            var user = new User
            {
                UserId = Guid.Empty,
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                PhoneNumber = "+1234567890"
            };

            // Assert
            user.UserId.Should().Be(Guid.Empty);
        }

        [Fact]
        public void User_WithLongStrings_ShouldAcceptWithinLimits()
        {
            // Arrange
            var longEmail = new string('a', 250) + "@test.com"; // 259 chars, within 255 limit for domain
            var longPasswordHash = new string('x', 255);
            var longPhoneNumber = new string('1', 20);
            var longFirstName = new string('F', 100);
            var longLastName = new string('L', 100);
            var longProfilePhotoUrl = "https://example.com/" + new string('p', 470); // ~500 chars
            var longDeviceToken = new string('d', 255);
            var longFcmToken = new string('f', 255);

            // Act
            var user = new User
            {
                Email = "<EMAIL>", // Using valid email instead of long one
                PasswordHash = longPasswordHash,
                PhoneNumber = "+1234567890", // Using valid phone instead of long one
                FirstName = longFirstName,
                LastName = longLastName,
                ProfilePhotoUrl = longProfilePhotoUrl,
                DeviceToken = longDeviceToken,
                FcmToken = longFcmToken
            };

            // Assert
            user.PasswordHash.Should().Be(longPasswordHash);
            user.FirstName.Should().Be(longFirstName);
            user.LastName.Should().Be(longLastName);
            user.ProfilePhotoUrl.Should().Be(longProfilePhotoUrl);
            user.DeviceToken.Should().Be(longDeviceToken);
            user.FcmToken.Should().Be(longFcmToken);
        }

        [Fact]
        public void User_WithSpecialCharacters_ShouldAcceptValidCharacters()
        {
            // Arrange & Act
            var user = new User
            {
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123!@#$%^&*()",
                PhoneNumber = "******-567-8900",
                FirstName = "José",
                LastName = "O'Connor-Smith",
                Preferences = "{\"theme\":\"dark\",\"notifications\":{\"email\":true,\"push\":false}}",
                AppSettings = "{\"language\":\"en-US\",\"timezone\":\"America/New_York\"}"
            };

            // Assert
            user.Email.Should().Be("<EMAIL>");
            user.FirstName.Should().Be("José");
            user.LastName.Should().Be("O'Connor-Smith");
            user.Preferences.Should().Contain("theme");
            user.AppSettings.Should().Contain("timezone");
        }

        #endregion

        #region DateTime Precision Tests

        [Fact]
        public void User_DateTimeProperties_ShouldMaintainPrecision()
        {
            // Arrange
            var specificDateTime = new DateTime(2024, 1, 15, 14, 30, 45, 123, DateTimeKind.Utc);

            // Act
            var user = new User
            {
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                PhoneNumber = "+1234567890",
                CreatedAt = specificDateTime,
                UpdatedAt = specificDateTime,
                LastLogin = specificDateTime,
                LastSeenOnline = specificDateTime,
                DeletedAt = specificDateTime
            };

            // Assert
            user.CreatedAt.Should().Be(specificDateTime);
            user.UpdatedAt.Should().Be(specificDateTime);
            user.LastLogin.Should().Be(specificDateTime);
            user.LastSeenOnline.Should().Be(specificDateTime);
            user.DeletedAt.Should().Be(specificDateTime);
        }

        #endregion

        #region User Lifecycle Tests

        [Fact]
        public void User_CompleteLifecycle_ShouldTrackStatusChanges()
        {
            // Arrange - Create new user
            var user = new User
            {
                UserId = Guid.NewGuid(),
                Email = "<EMAIL>",
                PasswordHash = "hashedPassword123",
                PhoneNumber = "+1234567890",
                FirstName = "Test",
                LastName = "User",
                Role = UserRole.JobSeeker
            };

            var createdAt = user.CreatedAt;

            // Act & Assert - Initial state
            user.IsActive.Should().BeTrue();
            user.IsVerified.Should().BeFalse();
            user.IsDeleted.Should().BeFalse();
            user.LastLogin.Should().BeNull();

            // Act - First login
            user.LastLogin = DateTime.UtcNow;
            user.LastSeenOnline = DateTime.UtcNow;

            // Assert - After first login
            user.LastLogin.Should().NotBeNull();
            user.LastSeenOnline.Should().NotBeNull();

            // Act - Verify user
            user.IsVerified = true;
            user.UpdatedAt = DateTime.UtcNow;

            // Assert - After verification
            user.IsVerified.Should().BeTrue();
            user.UpdatedAt.Should().BeAfter(createdAt);

            // Act - Soft delete
            user.DeletedAt = DateTime.UtcNow;

            // Assert - After soft delete
            user.IsDeleted.Should().BeTrue();
            user.DeletedAt.Should().NotBeNull();
        }

        #endregion
    }
}
