import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors } from '../../constants/Colors';

interface PasswordStrengthIndicatorProps {
  password: string;
}

interface PasswordCriteria {
  label: string;
  test: (password: string) => boolean;
}

const passwordCriteria: PasswordCriteria[] = [
  {
    label: 'At least 8 characters',
    test: (password: string) => password.length >= 8,
  },
  {
    label: 'One lowercase letter',
    test: (password: string) => /[a-z]/.test(password),
  },
  {
    label: 'One uppercase letter',
    test: (password: string) => /[A-Z]/.test(password),
  },
  {
    label: 'One number',
    test: (password: string) => /\d/.test(password),
  },
];

export const PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({
  password,
}) => {
  const getPasswordStrength = () => {
    const passedCriteria = passwordCriteria.filter(criteria => 
      criteria.test(password)
    ).length;
    
    if (passedCriteria === 0) return { strength: 'none', color: '#E0E0E0', label: '' };
    if (passedCriteria <= 1) return { strength: 'weak', color: Colors.light.danger, label: 'Weak' };
    if (passedCriteria <= 2) return { strength: 'fair', color: Colors.light.warning, label: 'Fair' };
    if (passedCriteria <= 3) return { strength: 'good', color: Colors.light.secondary, label: 'Good' };
    return { strength: 'strong', color: Colors.light.success, label: 'Strong' };
  };

  const { strength, color, label } = getPasswordStrength();
  const passedCriteria = passwordCriteria.filter(criteria => criteria.test(password));

  if (password.length === 0) return null;

  return (
    <View style={styles.container}>
      {/* Strength Bar */}
      <View style={styles.strengthBarContainer}>
        <View style={styles.strengthBarBackground}>
          <View
            style={[
              styles.strengthBarFill,
              {
                backgroundColor: color,
                width: `${(passedCriteria.length / passwordCriteria.length) * 100}%`,
              },
            ]}
          />
        </View>
        {label && <Text style={[styles.strengthLabel, { color }]}>{label}</Text>}
      </View>

      {/* Criteria List */}
      <View style={styles.criteriaContainer}>
        {passwordCriteria.map((criteria, index) => {
          const isPassed = criteria.test(password);
          return (
            <View key={index} style={styles.criteriaItem}>
              <Text
                style={[
                  styles.criteriaIcon,
                  { color: isPassed ? Colors.light.success : Colors.light.textTertiary },
                ]}
              >
                {isPassed ? '✓' : '○'}
              </Text>
              <Text
                style={[
                  styles.criteriaText,
                  { color: isPassed ? Colors.light.success : Colors.light.textTertiary },
                ]}
              >
                {criteria.label}
              </Text>
            </View>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 8,
    marginBottom: 4,
  },
  strengthBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  strengthBarBackground: {
    flex: 1,
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    overflow: 'hidden',
  },
  strengthBarFill: {
    height: '100%',
    borderRadius: 2,
    transition: 'width 0.3s ease',
  },
  strengthLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 8,
    minWidth: 40,
  },
  criteriaContainer: {
    gap: 4,
  },
  criteriaItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  criteriaIcon: {
    fontSize: 12,
    fontWeight: 'bold',
    marginRight: 6,
    width: 12,
  },
  criteriaText: {
    fontSize: 12,
    flex: 1,
  },
});
