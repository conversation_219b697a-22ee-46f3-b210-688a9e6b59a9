namespace Workforce.Application.Options
{
    /// <summary>
    /// Configuration options for account lockout settings
    /// </summary>
    public class AccountLockoutOptions
    {
        /// <summary>
        /// Maximum number of failed login attempts before account lockout
        /// </summary>
        public int MaxFailedAttempts { get; set; } = 5;

        /// <summary>
        /// Duration in minutes for account lockout
        /// </summary>
        public int LockoutDurationMinutes { get; set; } = 15;
    }
}
