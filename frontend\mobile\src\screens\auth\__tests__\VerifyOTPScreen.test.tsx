import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import VerifyOTPScreen from '../VerifyOTPScreen';

// Mock expo-router for this specific test
const mockPush = jest.fn();
const mockBack = jest.fn();
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: mockPush,
    back: mockBack,
    replace: jest.fn(),
  }),
}));

// Mock timers
jest.useFakeTimers();

describe('VerifyOTPScreen', () => {
  beforeEach(() => {
    jest.clearAllTimers();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
    jest.useFakeTimers();
  });

  it('renders correctly', () => {
    const { getByText, getAllByDisplayValue } = render(<VerifyOTPScreen />);
    
    expect(getByText('Verify Your Phone')).toBeTruthy();
    expect(getByText('Enter verification code')).toBeTruthy();
    expect(getByText('Verify')).toBeTruthy();
    
    // Should have 6 OTP input fields
    const otpInputs = getAllByDisplayValue('');
    expect(otpInputs).toHaveLength(6);
  });

  it('handles OTP input correctly', async () => {
    const { getAllByDisplayValue } = render(<VerifyOTPScreen />);
    
    const otpInputs = getAllByDisplayValue('');
    
    // Enter digits in sequence
    fireEvent.changeText(otpInputs[0], '1');
    fireEvent.changeText(otpInputs[1], '2');
    fireEvent.changeText(otpInputs[2], '3');
    
    await waitFor(() => {
      expect(otpInputs[0].props.value).toBe('1');
      expect(otpInputs[1].props.value).toBe('2');
      expect(otpInputs[2].props.value).toBe('3');
    });
  });

  it('only allows numeric input', async () => {
    const { getAllByDisplayValue } = render(<VerifyOTPScreen />);
    
    const otpInputs = getAllByDisplayValue('');
    
    // Try to enter non-numeric characters
    fireEvent.changeText(otpInputs[0], 'a');
    fireEvent.changeText(otpInputs[1], '!');
    fireEvent.changeText(otpInputs[2], '5');
    
    await waitFor(() => {
      expect(otpInputs[0].props.value).toBe('');
      expect(otpInputs[1].props.value).toBe('');
      expect(otpInputs[2].props.value).toBe('5');
    });
  });

  it('shows error for incomplete OTP', async () => {
    const { getByText, getAllByDisplayValue } = render(<VerifyOTPScreen />);
    
    const otpInputs = getAllByDisplayValue('');
    const verifyButton = getByText('Verify');
    
    // Enter incomplete OTP
    fireEvent.changeText(otpInputs[0], '1');
    fireEvent.changeText(otpInputs[1], '2');
    fireEvent.changeText(otpInputs[2], '3');
    
    fireEvent.press(verifyButton);
    
    await waitFor(() => {
      expect(getByText('Please enter the complete 6-digit code')).toBeTruthy();
    });
  });

  it('shows countdown timer initially', () => {
    const { getByText } = render(<VerifyOTPScreen />);
    
    expect(getByText('Resend code in 1:00')).toBeTruthy();
  });

  it('enables resend button after countdown', async () => {
    const { getByText, queryByText } = render(<VerifyOTPScreen />);
    
    // Fast-forward time by 60 seconds
    act(() => {
      jest.advanceTimersByTime(60000);
    });
    
    await waitFor(() => {
      expect(queryByText(/Resend code in/)).toBeNull();
      expect(getByText('Resend Code')).toBeTruthy();
    });
  });

  it('handles back button press', () => {
    const mockBack = jest.fn();
    jest.doMock('expo-router', () => ({
      useRouter: () => ({
        push: jest.fn(),
        back: mockBack,
      }),
    }));

    const { getByText } = render(<VerifyOTPScreen />);
    const backButton = getByText('←');
    
    fireEvent.press(backButton);
    
    expect(mockBack).toHaveBeenCalled();
  });

  it('clears error when user starts typing', async () => {
    const { getByText, getAllByDisplayValue } = render(<VerifyOTPScreen />);

    const otpInputs = getAllByDisplayValue('');
    const verifyButton = getByText('Verify');

    // Trigger error first
    fireEvent.press(verifyButton);

    await waitFor(() => {
      expect(getByText('Please enter the complete 6-digit code')).toBeTruthy();
    });

    // Start typing to clear error
    fireEvent.changeText(otpInputs[0], '1');

    await waitFor(() => {
      expect(() => getByText('Please enter the complete 6-digit code')).toThrow();
    });
  });

  it('successfully verifies valid OTP and navigates to role selection', async () => {
    const { getByText, getAllByDisplayValue } = render(<VerifyOTPScreen />);

    const otpInputs = getAllByDisplayValue('');
    const verifyButton = getByText('Verify');

    // Enter valid OTP (any 6-digit code except 000000)
    fireEvent.changeText(otpInputs[0], '1');
    fireEvent.changeText(otpInputs[1], '2');
    fireEvent.changeText(otpInputs[2], '3');
    fireEvent.changeText(otpInputs[3], '4');
    fireEvent.changeText(otpInputs[4], '5');
    fireEvent.changeText(otpInputs[5], '6');

    fireEvent.press(verifyButton);

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/auth/role-selection');
    });
  });

  it('shows error for invalid OTP (000000)', async () => {
    const { getByText, getAllByDisplayValue } = render(<VerifyOTPScreen />);

    const otpInputs = getAllByDisplayValue('');
    const verifyButton = getByText('Verify');

    // Enter invalid OTP
    fireEvent.changeText(otpInputs[0], '0');
    fireEvent.changeText(otpInputs[1], '0');
    fireEvent.changeText(otpInputs[2], '0');
    fireEvent.changeText(otpInputs[3], '0');
    fireEvent.changeText(otpInputs[4], '0');
    fireEvent.changeText(otpInputs[5], '0');

    fireEvent.press(verifyButton);

    await waitFor(() => {
      expect(getByText('Invalid verification code. Please try again.')).toBeTruthy();
    });
  });

  it('handles resend code functionality', async () => {
    const { getByText } = render(<VerifyOTPScreen />);

    // Fast-forward time to enable resend
    act(() => {
      jest.advanceTimersByTime(60000);
    });

    await waitFor(() => {
      const resendButton = getByText('Resend Code');
      fireEvent.press(resendButton);

      // Should restart the timer
      expect(getByText('Resend code in 1:00')).toBeTruthy();
    });
  });

  it('displays loading state during verification', async () => {
    const { getByText, getAllByDisplayValue } = render(<VerifyOTPScreen />);

    const otpInputs = getAllByDisplayValue('');
    const verifyButton = getByText('Verify');

    // Enter valid OTP
    fireEvent.changeText(otpInputs[0], '1');
    fireEvent.changeText(otpInputs[1], '2');
    fireEvent.changeText(otpInputs[2], '3');
    fireEvent.changeText(otpInputs[3], '4');
    fireEvent.changeText(otpInputs[4], '5');
    fireEvent.changeText(otpInputs[5], '6');

    fireEvent.press(verifyButton);

    // Should show loading state
    expect(getByText('Verifying...')).toBeTruthy();
  });

  it('shows proper instructions and phone number', () => {
    const { getByText } = render(<VerifyOTPScreen />);

    expect(getByText('Enter verification code')).toBeTruthy();
    expect(getByText('We sent a 6-digit code to your phone number')).toBeTruthy();
    expect(getByText('+****************')).toBeTruthy();
  });
});
