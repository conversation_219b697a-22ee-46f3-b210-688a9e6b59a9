import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import VerifyOTPScreen from '../VerifyOTPScreen';

// Mock expo-router
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
  }),
}));

// Mock expo-linear-gradient
jest.mock('expo-linear-gradient', () => ({
  LinearGradient: ({ children, ...props }: any) => children,
}));

// Mock timers
jest.useFakeTimers();

describe('VerifyOTPScreen', () => {
  beforeEach(() => {
    jest.clearAllTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
    jest.useFakeTimers();
  });

  it('renders correctly', () => {
    const { getByText, getAllByDisplayValue } = render(<VerifyOTPScreen />);
    
    expect(getByText('Verify Your Phone')).toBeTruthy();
    expect(getByText('Enter verification code')).toBeTruthy();
    expect(getByText('Verify')).toBeTruthy();
    
    // Should have 6 OTP input fields
    const otpInputs = getAllByDisplayValue('');
    expect(otpInputs).toHaveLength(6);
  });

  it('handles OTP input correctly', async () => {
    const { getAllByDisplayValue } = render(<VerifyOTPScreen />);
    
    const otpInputs = getAllByDisplayValue('');
    
    // Enter digits in sequence
    fireEvent.changeText(otpInputs[0], '1');
    fireEvent.changeText(otpInputs[1], '2');
    fireEvent.changeText(otpInputs[2], '3');
    
    await waitFor(() => {
      expect(otpInputs[0].props.value).toBe('1');
      expect(otpInputs[1].props.value).toBe('2');
      expect(otpInputs[2].props.value).toBe('3');
    });
  });

  it('only allows numeric input', async () => {
    const { getAllByDisplayValue } = render(<VerifyOTPScreen />);
    
    const otpInputs = getAllByDisplayValue('');
    
    // Try to enter non-numeric characters
    fireEvent.changeText(otpInputs[0], 'a');
    fireEvent.changeText(otpInputs[1], '!');
    fireEvent.changeText(otpInputs[2], '5');
    
    await waitFor(() => {
      expect(otpInputs[0].props.value).toBe('');
      expect(otpInputs[1].props.value).toBe('');
      expect(otpInputs[2].props.value).toBe('5');
    });
  });

  it('shows error for incomplete OTP', async () => {
    const { getByText, getAllByDisplayValue } = render(<VerifyOTPScreen />);
    
    const otpInputs = getAllByDisplayValue('');
    const verifyButton = getByText('Verify');
    
    // Enter incomplete OTP
    fireEvent.changeText(otpInputs[0], '1');
    fireEvent.changeText(otpInputs[1], '2');
    fireEvent.changeText(otpInputs[2], '3');
    
    fireEvent.press(verifyButton);
    
    await waitFor(() => {
      expect(getByText('Please enter the complete 6-digit code')).toBeTruthy();
    });
  });

  it('shows countdown timer initially', () => {
    const { getByText } = render(<VerifyOTPScreen />);
    
    expect(getByText('Resend code in 1:00')).toBeTruthy();
  });

  it('enables resend button after countdown', async () => {
    const { getByText, queryByText } = render(<VerifyOTPScreen />);
    
    // Fast-forward time by 60 seconds
    act(() => {
      jest.advanceTimersByTime(60000);
    });
    
    await waitFor(() => {
      expect(queryByText(/Resend code in/)).toBeNull();
      expect(getByText('Resend Code')).toBeTruthy();
    });
  });

  it('handles back button press', () => {
    const mockBack = jest.fn();
    jest.doMock('expo-router', () => ({
      useRouter: () => ({
        push: jest.fn(),
        back: mockBack,
      }),
    }));

    const { getByText } = render(<VerifyOTPScreen />);
    const backButton = getByText('←');
    
    fireEvent.press(backButton);
    
    expect(mockBack).toHaveBeenCalled();
  });

  it('clears error when user starts typing', async () => {
    const { getByText, getAllByDisplayValue } = render(<VerifyOTPScreen />);
    
    const otpInputs = getAllByDisplayValue('');
    const verifyButton = getByText('Verify');
    
    // Trigger error first
    fireEvent.press(verifyButton);
    
    await waitFor(() => {
      expect(getByText('Please enter the complete 6-digit code')).toBeTruthy();
    });
    
    // Start typing to clear error
    fireEvent.changeText(otpInputs[0], '1');
    
    await waitFor(() => {
      expect(() => getByText('Please enter the complete 6-digit code')).toThrow();
    });
  });
});
