import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import RoleSelectionScreen from '../RoleSelectionScreen';

// Mock expo-router for this specific test
const mockPush = jest.fn();
const mockBack = jest.fn();
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: mockPush,
    back: mockBack,
    replace: jest.fn(),
  }),
}));

describe('RoleSelectionScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    const { getByText } = render(<RoleSelectionScreen />);
    
    expect(getByText('Choose Your Role')).toBeTruthy();
    expect(getByText('Select how you\'d like to use HireNow to get started')).toBeTruthy();
    expect(getByText('Job Seeker')).toBeTruthy();
    expect(getByText('Employer')).toBeTruthy();
    expect(getByText('Continue')).toBeTruthy();
  });

  it('displays role descriptions and features', () => {
    const { getByText } = render(<RoleSelectionScreen />);
    
    // Job Seeker features
    expect(getByText('Find flexible work opportunities that match your skills and schedule')).toBeTruthy();
    expect(getByText('Browse available jobs')).toBeTruthy();
    expect(getByText('Apply with one tap')).toBeTruthy();
    expect(getByText('Flexible scheduling')).toBeTruthy();
    
    // Employer features
    expect(getByText('Post jobs and find qualified workers for your business needs')).toBeTruthy();
    expect(getByText('Post job opportunities')).toBeTruthy();
    expect(getByText('Review applications')).toBeTruthy();
    expect(getByText('Manage your workforce')).toBeTruthy();
  });

  it('allows role selection', async () => {
    const { getByText } = render(<RoleSelectionScreen />);
    
    const jobSeekerCard = getByText('Job Seeker').closest('View');
    const continueButton = getByText('Continue');
    
    // Initially continue button should be disabled
    expect(continueButton.props.accessibilityState?.disabled).toBe(true);
    
    // Select Job Seeker role
    if (jobSeekerCard) {
      fireEvent.press(jobSeekerCard);
    }
    
    await waitFor(() => {
      // Continue button should be enabled after selection
      expect(continueButton.props.accessibilityState?.disabled).toBe(false);
    });
  });

  it('switches between role selections', async () => {
    const { getByText } = render(<RoleSelectionScreen />);
    
    const jobSeekerCard = getByText('Job Seeker').closest('View');
    const employerCard = getByText('Employer').closest('View');
    
    // Select Job Seeker first
    if (jobSeekerCard) {
      fireEvent.press(jobSeekerCard);
    }
    
    // Then select Employer
    if (employerCard) {
      fireEvent.press(employerCard);
    }
    
    // Both actions should work without errors
    await waitFor(() => {
      expect(getByText('Continue')).toBeTruthy();
    });
  });

  it('handles continue button press', async () => {
    const { getByText } = render(<RoleSelectionScreen />);

    const jobSeekerCard = getByText('Job Seeker').closest('View');
    const continueButton = getByText('Continue');

    // Select a role first
    if (jobSeekerCard) {
      fireEvent.press(jobSeekerCard);
    }

    // Press continue
    fireEvent.press(continueButton);

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/auth/profile-setup?role=job_seeker');
    });
  });

  it('handles back button press', () => {
    const { getByText } = render(<RoleSelectionScreen />);
    const backButton = getByText('←');

    fireEvent.press(backButton);

    expect(mockBack).toHaveBeenCalled();
  });

  it('shows help text', () => {
    const { getByText } = render(<RoleSelectionScreen />);
    
    expect(getByText('You can change your role later in settings')).toBeTruthy();
  });

  it('displays role icons', () => {
    const { getByText } = render(<RoleSelectionScreen />);

    expect(getByText('👤')).toBeTruthy(); // Job Seeker icon
    expect(getByText('🏢')).toBeTruthy(); // Employer icon
  });

  it('handles employer role selection and navigation', async () => {
    const { getByText } = render(<RoleSelectionScreen />);

    const employerCard = getByText('Employer').closest('View');
    const continueButton = getByText('Continue');

    // Select employer role
    if (employerCard) {
      fireEvent.press(employerCard);
    }

    // Press continue
    fireEvent.press(continueButton);

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/auth/profile-setup?role=employer');
    });
  });

  it('disables continue button when no role is selected', () => {
    const { getByText } = render(<RoleSelectionScreen />);

    const continueButton = getByText('Continue');

    // Button should be disabled initially
    expect(continueButton.props.accessibilityState?.disabled).toBe(true);
  });

  it('enables continue button when a role is selected', async () => {
    const { getByText } = render(<RoleSelectionScreen />);

    const jobSeekerCard = getByText('Job Seeker').closest('View');
    const continueButton = getByText('Continue');

    // Select a role
    if (jobSeekerCard) {
      fireEvent.press(jobSeekerCard);
    }

    await waitFor(() => {
      expect(continueButton.props.accessibilityState?.disabled).toBe(false);
    });
  });

  it('shows loading state during role selection submission', async () => {
    const { getByText } = render(<RoleSelectionScreen />);

    const jobSeekerCard = getByText('Job Seeker').closest('View');
    const continueButton = getByText('Continue');

    // Select a role
    if (jobSeekerCard) {
      fireEvent.press(jobSeekerCard);
    }

    // Press continue
    fireEvent.press(continueButton);

    // Should show loading state
    expect(getByText('Loading...')).toBeTruthy();
  });

  it('displays proper role selection visual feedback', async () => {
    const { getByText } = render(<RoleSelectionScreen />);

    const jobSeekerCard = getByText('Job Seeker').closest('View');
    const employerCard = getByText('Employer').closest('View');

    // Select job seeker
    if (jobSeekerCard) {
      fireEvent.press(jobSeekerCard);
    }

    // Then select employer (should deselect job seeker)
    if (employerCard) {
      fireEvent.press(employerCard);
    }

    // Both actions should work without errors
    await waitFor(() => {
      expect(getByText('Continue')).toBeTruthy();
    });
  });
});
