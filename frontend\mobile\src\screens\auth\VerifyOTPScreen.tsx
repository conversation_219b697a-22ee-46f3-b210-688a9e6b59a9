import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  StatusBar,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Colors } from '../../constants/Colors';
import { CustomButton } from '../../components/common/CustomButton';
import { OTPInput } from '../../components/auth/OTPInput';

export default function VerifyOTPScreen() {
  const router = useRouter();
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [resendTimer, setResendTimer] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    // Start countdown timer
    if (resendTimer > 0) {
      const timer = setTimeout(() => {
        setResendTimer(resendTimer - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [resendTimer]);

  const handleOtpChange = (newOtp: string[]) => {
    setOtp(newOtp);
    setError(''); // Clear error when user types
  };

  const handleVerifyOTP = async () => {
    const otpCode = otp.join('');
    
    if (otpCode.length !== 6) {
      setError('Please enter the complete 6-digit code');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // For demo purposes, accept any 6-digit code
      // In real app, this would validate against the server
      if (otpCode === '000000') {
        setError('Invalid verification code. Please try again.');
        return;
      }

      // Navigate to role selection
      router.push('/auth/role-selection');
    } catch (error) {
      setError('Verification failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    if (!canResend) return;

    try {
      setIsLoading(true);
      setError('');

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Reset timer
      setResendTimer(60);
      setCanResend(false);
      setOtp(['', '', '', '', '', '']);

      Alert.alert('Success', 'Verification code has been resent to your phone.');
    } catch (error) {
      setError('Failed to resend code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  const formatTimer = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.light.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleBack}
          activeOpacity={0.7}
        >
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        
        <View style={styles.iconContainer}>
          <Text style={styles.icon}>📱</Text>
        </View>
        
        <Text style={styles.title}>Verify Your Phone</Text>
        <Text style={styles.subtitle}>
          We've sent a 6-digit verification code to{'\n'}
          <Text style={styles.phoneNumber}>+60 ************</Text>
        </Text>
      </View>

      {/* OTP Input */}
      <View style={styles.otpContainer}>
        <Text style={styles.otpLabel}>Enter verification code</Text>
        
        <OTPInput
          length={6}
          value={otp}
          onChange={handleOtpChange}
          error={!!error}
          autoFocus={true}
        />

        {error && <Text style={styles.errorText}>{error}</Text>}
      </View>

      {/* Actions */}
      <View style={styles.actions}>
        <CustomButton
          title="Verify"
          onPress={handleVerifyOTP}
          loading={isLoading}
          disabled={otp.join('').length !== 6 || isLoading}
          style={styles.verifyButton}
        />

        {/* Resend Code */}
        <View style={styles.resendContainer}>
          {canResend ? (
            <TouchableOpacity
              style={styles.resendButton}
              onPress={handleResendOTP}
              activeOpacity={0.7}
            >
              <Text style={styles.resendButtonText}>Resend Code</Text>
            </TouchableOpacity>
          ) : (
            <Text style={styles.timerText}>
              Resend code in {formatTimer(resendTimer)}
            </Text>
          )}
        </View>

        {/* Help Text */}
        <TouchableOpacity
          style={styles.helpContainer}
          activeOpacity={0.7}
        >
          <Text style={styles.helpText}>
            Didn't receive the code? Check your spam folder or{' '}
            <Text style={styles.helpLink}>contact support</Text>
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
    paddingHorizontal: 20,
  },
  header: {
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 40,
  },
  backButton: {
    position: 'absolute',
    left: 0,
    top: 60,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.light.card,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  backButtonText: {
    fontSize: 20,
    color: Colors.light.text,
    fontWeight: '600',
  },
  iconContainer: {
    width: 80,
    height: 80,
    backgroundColor: 'rgba(74, 111, 255, 0.1)',
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
    marginTop: 20,
  },
  icon: {
    fontSize: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 15,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  phoneNumber: {
    fontWeight: '600',
    color: Colors.light.primary,
  },
  otpContainer: {
    marginBottom: 40,
  },
  otpLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.text,
    marginBottom: 20,
    textAlign: 'center',
  },

  errorText: {
    fontSize: 14,
    color: Colors.light.danger,
    textAlign: 'center',
    marginTop: 10,
  },
  actions: {
    flex: 1,
    justifyContent: 'flex-start',
  },
  verifyButton: {
    marginBottom: 30,
  },
  resendContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  resendButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  resendButtonText: {
    fontSize: 16,
    color: Colors.light.primary,
    fontWeight: '600',
  },
  timerText: {
    fontSize: 16,
    color: Colors.light.textSecondary,
  },
  helpContainer: {
    alignItems: 'center',
    paddingVertical: 10,
  },
  helpText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  helpLink: {
    color: Colors.light.primary,
    fontWeight: '500',
    textDecorationLine: 'underline',
  },
});
