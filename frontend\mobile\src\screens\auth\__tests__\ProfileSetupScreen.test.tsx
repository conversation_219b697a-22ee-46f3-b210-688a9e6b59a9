import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import ProfileSetupScreen from '../ProfileSetupScreen';

// Mock expo-router for this specific test
const mockReplace = jest.fn();
const mockBack = jest.fn();
jest.mock('expo-router', () => ({
  useRouter: () => ({
    replace: mockReplace,
    back: mockBack,
    push: jest.fn(),
  }),
  useLocalSearchParams: () => ({
    role: 'job_seeker',
  }),
}));

describe('ProfileSetupScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly for job seeker', () => {
    const { getByText, getByPlaceholderText } = render(<ProfileSetupScreen />);
    
    expect(getByText('Complete Your Profile')).toBeTruthy();
    expect(getByText('Help employers find you by completing your profile')).toBeTruthy();
    expect(getByText('Profile Photo')).toBeTruthy();
    expect(getByPlaceholderText('Tell employers about yourself, your experience, and what you\'re looking for...')).toBeTruthy();
    expect(getByText('Skills')).toBeTruthy();
    expect(getByText('Complete Setup')).toBeTruthy();
  });

  it('renders correctly for employer', () => {
    // Mock employer role
    jest.doMock('expo-router', () => ({
      useRouter: () => ({
        replace: jest.fn(),
        back: jest.fn(),
      }),
      useLocalSearchParams: () => ({
        role: 'employer',
      }),
    }));

    const { getByText, getByPlaceholderText } = render(<ProfileSetupScreen />);
    
    expect(getByText('Complete Your Profile')).toBeTruthy();
    expect(getByText('Set up your company profile to attract the best talent')).toBeTruthy();
    expect(getByText('Company Logo')).toBeTruthy();
    expect(getByPlaceholderText('Your Company Ltd.')).toBeTruthy();
    expect(getByText('Company Name')).toBeTruthy();
  });

  it('shows validation errors for required fields', async () => {
    const { getByText } = render(<ProfileSetupScreen />);
    
    const submitButton = getByText('Complete Setup');
    
    // Submit button should be disabled initially
    expect(submitButton.props.accessibilityState?.disabled).toBe(true);
  });

  it('handles profile image selection', async () => {
    const { getByText } = render(<ProfileSetupScreen />);
    
    const imagePickerButton = getByText('Tap to add photo');
    fireEvent.press(imagePickerButton);
    
    // Should show image selection alert
    await waitFor(() => {
      // Alert should be shown (mocked in test environment)
      expect(imagePickerButton).toBeTruthy();
    });
  });

  it('handles back button press', () => {
    const mockBack = jest.fn();
    jest.doMock('expo-router', () => ({
      useRouter: () => ({
        replace: jest.fn(),
        back: mockBack,
      }),
      useLocalSearchParams: () => ({
        role: 'job_seeker',
      }),
    }));

    const { getByText } = render(<ProfileSetupScreen />);
    const backButton = getByText('←');
    
    fireEvent.press(backButton);
    
    expect(mockBack).toHaveBeenCalled();
  });

  it('validates bio length for job seekers', async () => {
    const { getByPlaceholderText, getByText } = render(<ProfileSetupScreen />);
    
    const bioInput = getByPlaceholderText('Tell employers about yourself, your experience, and what you\'re looking for...');
    
    // Test minimum length validation
    fireEvent.changeText(bioInput, 'Short bio');
    fireEvent(bioInput, 'blur');
    
    await waitFor(() => {
      expect(getByText('Bio must be at least 20 characters')).toBeTruthy();
    });
  });

  it('validates hourly rate format for job seekers', async () => {
    const { getByPlaceholderText, getByText } = render(<ProfileSetupScreen />);
    
    const hourlyRateInput = getByPlaceholderText('25.00');
    
    // Test invalid format
    fireEvent.changeText(hourlyRateInput, 'invalid');
    fireEvent(hourlyRateInput, 'blur');
    
    await waitFor(() => {
      expect(getByText('Please enter a valid hourly rate')).toBeTruthy();
    });
  });

  it('shows skills selector for job seekers', () => {
    const { getByText } = render(<ProfileSetupScreen />);
    
    expect(getByText('Skills')).toBeTruthy();
    expect(getByText('Popular Skills:')).toBeTruthy();
  });

  it('shows location selector', () => {
    const { getByText } = render(<ProfileSetupScreen />);
    
    expect(getByText('Location')).toBeTruthy();
    expect(getByText('Select your location')).toBeTruthy();
  });

  it('enables submit button when all required fields are filled', async () => {
    const { getByText, getByPlaceholderText } = render(<ProfileSetupScreen />);

    // Fill required fields
    const bioInput = getByPlaceholderText('Tell employers about yourself, your experience, and what I\'m looking for...');
    fireEvent.changeText(bioInput, 'This is a detailed bio about my experience and what I am looking for in my career.');

    const experienceInput = getByPlaceholderText('e.g., Entry Level, 2-5 years, Senior');
    fireEvent.changeText(experienceInput, '2-5 years');

    const hourlyRateInput = getByPlaceholderText('25.00');
    fireEvent.changeText(hourlyRateInput, '25.00');

    // Note: In a real test, we would also need to select skills and location
    // but those require more complex interaction with custom components

    const submitButton = getByText('Complete Setup');
    expect(submitButton).toBeTruthy();
  });

  it('successfully submits profile and navigates to main app', async () => {
    const { getByText, getByPlaceholderText } = render(<ProfileSetupScreen />);

    // Fill all required fields
    const bioInput = getByPlaceholderText('Tell employers about yourself, your experience, and what I\'m looking for...');
    fireEvent.changeText(bioInput, 'This is a detailed bio about my experience and what I am looking for in my career.');

    const experienceInput = getByPlaceholderText('e.g., Entry Level, 2-5 years, Senior');
    fireEvent.changeText(experienceInput, '2-5 years');

    const hourlyRateInput = getByPlaceholderText('25.00');
    fireEvent.changeText(hourlyRateInput, '25.00');

    // Submit the form
    const submitButton = getByText('Complete Setup');
    fireEvent.press(submitButton);

    await waitFor(() => {
      expect(mockReplace).toHaveBeenCalledWith('/(tabs)');
    });
  });

  it('shows loading state during profile submission', async () => {
    const { getByText, getByPlaceholderText } = render(<ProfileSetupScreen />);

    // Fill required fields
    const bioInput = getByPlaceholderText('Tell employers about yourself, your experience, and what I\'m looking for...');
    fireEvent.changeText(bioInput, 'This is a detailed bio about my experience and what I am looking for in my career.');

    const experienceInput = getByPlaceholderText('e.g., Entry Level, 2-5 years, Senior');
    fireEvent.changeText(experienceInput, '2-5 years');

    const hourlyRateInput = getByPlaceholderText('25.00');
    fireEvent.changeText(hourlyRateInput, '25.00');

    // Submit the form
    const submitButton = getByText('Complete Setup');
    fireEvent.press(submitButton);

    // Should show loading state
    expect(getByText('Completing Setup...')).toBeTruthy();
  });

  it('handles back navigation', () => {
    const { getByText } = render(<ProfileSetupScreen />);

    const backButton = getByText('←');
    fireEvent.press(backButton);

    expect(mockBack).toHaveBeenCalled();
  });

  it('validates company name for employers', async () => {
    // Mock employer role
    jest.doMock('expo-router', () => ({
      useRouter: () => ({
        replace: mockReplace,
        back: mockBack,
        push: jest.fn(),
      }),
      useLocalSearchParams: () => ({
        role: 'employer',
      }),
    }));

    const { getByPlaceholderText, getByText } = render(<ProfileSetupScreen />);

    const companyNameInput = getByPlaceholderText('Your Company Ltd.');

    // Test empty company name
    fireEvent.changeText(companyNameInput, '');
    fireEvent(companyNameInput, 'blur');

    await waitFor(() => {
      expect(getByText('Company name is required')).toBeTruthy();
    });
  });

  it('shows different content for employer role', () => {
    // Mock employer role
    jest.doMock('expo-router', () => ({
      useRouter: () => ({
        replace: mockReplace,
        back: mockBack,
        push: jest.fn(),
      }),
      useLocalSearchParams: () => ({
        role: 'employer',
      }),
    }));

    const { getByText } = render(<ProfileSetupScreen />);

    expect(getByText('Set up your company profile to attract the best talent')).toBeTruthy();
    expect(getByText('Company Logo')).toBeTruthy();
    expect(getByText('Company Name')).toBeTruthy();
  });
});
