using Microsoft.EntityFrameworkCore;
using Workforce.Domain.Entities;
using Workforce.Domain.Enums;
using Workforce.Domain.Repositories;
using Workforce.Infrastructure.Data;

namespace Workforce.Infrastructure.Repositories
{
    /// <summary>
    /// Repository implementation for User entity operations
    /// </summary>
    public class UserRepository : Repository<User>, IUserRepository
    {
        public UserRepository(ApplicationDbContext context) : base(context)
        {
        }

        /// <summary>
        /// Gets a user by their email address
        /// </summary>
        /// <param name="email">The email address to search for</param>
        /// <returns>The user if found, null otherwise</returns>
        public async Task<User?> GetByEmailAsync(string email)
        {
            return await _context.Set<User>()
                .FirstOrDefaultAsync(u => u.Email.ToLower() == email.ToLower() && u.DeletedAt == null);
        }

        /// <summary>
        /// Gets a user by their phone number
        /// </summary>
        /// <param name="phoneNumber">The phone number to search for</param>
        /// <returns>The user if found, null otherwise</returns>
        public async Task<User?> GetByPhoneNumberAsync(string phoneNumber)
        {
            return await _context.Set<User>()
                .FirstOrDefaultAsync(u => u.PhoneNumber == phoneNumber && u.DeletedAt == null);
        }

        /// <summary>
        /// Checks if an email address is already in use
        /// </summary>
        /// <param name="email">The email address to check</param>
        /// <param name="excludeUserId">Optional user ID to exclude from the check (for updates)</param>
        /// <returns>True if email exists, false otherwise</returns>
        public async Task<bool> EmailExistsAsync(string email, Guid? excludeUserId = null)
        {
            var query = _context.Set<User>()
                .Where(u => u.Email.ToLower() == email.ToLower() && u.DeletedAt == null);

            if (excludeUserId.HasValue)
            {
                query = query.Where(u => u.UserId != excludeUserId.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// Checks if a phone number is already in use
        /// </summary>
        /// <param name="phoneNumber">The phone number to check</param>
        /// <param name="excludeUserId">Optional user ID to exclude from the check (for updates)</param>
        /// <returns>True if phone number exists, false otherwise</returns>
        public async Task<bool> PhoneNumberExistsAsync(string phoneNumber, Guid? excludeUserId = null)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            var query = _context.Set<User>()
                .Where(u => u.PhoneNumber == phoneNumber && u.DeletedAt == null);

            if (excludeUserId.HasValue)
            {
                query = query.Where(u => u.UserId != excludeUserId.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// Gets users by their role
        /// </summary>
        /// <param name="role">The user role to filter by</param>
        /// <returns>Collection of users with the specified role</returns>
        public async Task<IEnumerable<User>> GetByRoleAsync(UserRole role)
        {
            return await _context.Set<User>()
                .Where(u => u.Role == role && u.DeletedAt == null)
                .OrderBy(u => u.FirstName)
                .ThenBy(u => u.LastName)
                .ToListAsync();
        }

        /// <summary>
        /// Gets active users (not soft deleted)
        /// </summary>
        /// <returns>Collection of active users</returns>
        public async Task<IEnumerable<User>> GetActiveUsersAsync()
        {
            return await _context.Set<User>()
                .Where(u => u.IsActive && u.DeletedAt == null)
                .OrderBy(u => u.FirstName)
                .ThenBy(u => u.LastName)
                .ToListAsync();
        }

        /// <summary>
        /// Updates the user's last login timestamp
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="loginTime">The login timestamp</param>
        public async Task UpdateLastLoginAsync(Guid userId, DateTime loginTime)
        {
            await _context.Set<User>()
                .Where(u => u.UserId == userId && u.DeletedAt == null)
                .ExecuteUpdateAsync(setters => setters
                    .SetProperty(u => u.LastLogin, loginTime)
                    .SetProperty(u => u.UpdatedAt, DateTime.UtcNow));
        }

        /// <summary>
        /// Increments the failed login attempts for a user
        /// </summary>
        /// <param name="userId">The user ID</param>
        public async Task IncrementFailedLoginAttemptsAsync(Guid userId)
        {
            await _context.Set<User>()
                .Where(u => u.UserId == userId && u.DeletedAt == null)
                .ExecuteUpdateAsync(setters => setters
                    .SetProperty(u => u.FailedLoginAttempts, u => u.FailedLoginAttempts + 1)
                    .SetProperty(u => u.UpdatedAt, DateTime.UtcNow));
        }

        /// <summary>
        /// Atomically increments the failed login attempts for a user and returns the new count
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>The new failed login attempts count after incrementing</returns>
        public async Task<int> IncrementFailedLoginAttemptsAndGetCountAsync(Guid userId)
        {
            // Check if we're using in-memory database (for testing)
            var isInMemory = _context.Database.ProviderName == "Microsoft.EntityFrameworkCore.InMemory";

            if (isInMemory)
            {
                // For in-memory database, use traditional entity tracking approach
                var user = await _context.Set<User>()
                    .FirstOrDefaultAsync(u => u.UserId == userId && u.DeletedAt == null);

                if (user == null)
                {
                    return 0;
                }

                user.FailedLoginAttempts++;
                user.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                return user.FailedLoginAttempts;
            }
            else
            {
                // For SQL databases, use atomic UPDATE statement
                var rowsAffected = await _context.Set<User>()
                    .Where(u => u.UserId == userId && u.DeletedAt == null)
                    .ExecuteUpdateAsync(setters => setters
                        .SetProperty(u => u.FailedLoginAttempts, u => u.FailedLoginAttempts + 1)
                        .SetProperty(u => u.UpdatedAt, DateTime.UtcNow));

                // If no rows were affected, the user doesn't exist
                if (rowsAffected == 0)
                {
                    return 0;
                }

                // Get the updated count
                var newCount = await _context.Set<User>()
                    .Where(u => u.UserId == userId && u.DeletedAt == null)
                    .Select(u => u.FailedLoginAttempts)
                    .FirstOrDefaultAsync();

                return newCount;
            }
        }

        /// <summary>
        /// Resets the failed login attempts for a user
        /// </summary>
        /// <param name="userId">The user ID</param>
        public async Task ResetFailedLoginAttemptsAsync(Guid userId)
        {
            await _context.Set<User>()
                .Where(u => u.UserId == userId && u.DeletedAt == null)
                .ExecuteUpdateAsync(setters => setters
                    .SetProperty(u => u.FailedLoginAttempts, 0)
                    .SetProperty(u => u.UpdatedAt, DateTime.UtcNow));
        }

        /// <summary>
        /// Locks out a user account until the specified time
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="lockoutEnd">When the lockout ends</param>
        public async Task LockoutUserAsync(Guid userId, DateTime lockoutEnd)
        {
            await _context.Set<User>()
                .Where(u => u.UserId == userId && u.DeletedAt == null)
                .ExecuteUpdateAsync(setters => setters
                    .SetProperty(u => u.LockoutEnd, lockoutEnd)
                    .SetProperty(u => u.UpdatedAt, DateTime.UtcNow));
        }

        /// <summary>
        /// Unlocks a user account
        /// </summary>
        /// <param name="userId">The user ID</param>
        public async Task UnlockUserAsync(Guid userId)
        {
            await _context.Set<User>()
                .Where(u => u.UserId == userId && u.DeletedAt == null)
                .ExecuteUpdateAsync(setters => setters
                    .SetProperty(u => u.LockoutEnd, (DateTime?)null)
                    .SetProperty(u => u.FailedLoginAttempts, 0)
                    .SetProperty(u => u.UpdatedAt, DateTime.UtcNow));
        }

        /// <summary>
        /// Updates the user's last seen online timestamp
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="lastSeenTime">The last seen timestamp</param>
        public async Task UpdateLastSeenOnlineAsync(Guid userId, DateTime lastSeenTime)
        {
            var user = await GetByIdAsync(userId);
            if (user != null)
            {
                user.LastSeenOnline = lastSeenTime;
                user.UpdatedAt = DateTime.UtcNow;
                await UpdateAsync(user);
            }
        }
    }
}
