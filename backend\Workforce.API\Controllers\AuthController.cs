using AutoMapper;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;
using Workforce.Application.Features.Users.Commands.RegisterUser;
using Workforce.Application.Features.Users.Commands.LoginUser;
using Workforce.Shared.DTOs;

namespace Workforce.API.Controllers
{
    /// <summary>
    /// Controller for authentication operations
    /// </summary>
    [ApiController]
    [Route("api/v1/[controller]")]
    [Produces("application/json")]
    public class AuthController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly IMapper _mapper;
        private readonly ILogger<AuthController> _logger;

        public AuthController(IMediator mediator, IMapper mapper, ILogger<AuthController> logger)
        {
            _mediator = mediator;
            _mapper = mapper;
            _logger = logger;
        }

        /// <summary>
        /// Registers a new user account
        /// </summary>
        /// <param name="registerUserDto">User registration data</param>
        /// <returns>Authenticated user with tokens</returns>
        /// <response code="201">User successfully registered</response>
        /// <response code="400">Invalid input data or validation errors</response>
        /// <response code="409">Email or phone number already exists</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("register")]
        [ProducesResponseType(typeof(AuthenticatedUserDto), StatusCodes.Status201Created)]
        [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status409Conflict)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<AuthenticatedUserDto>> Register([FromBody] RegisterUserDto registerUserDto)
        {
            try
            {
                _logger.LogInformation("User registration attempt for email: {Email}",
                    MaskEmailForLogging(registerUserDto.Email));

                // Map DTO to command
                var command = _mapper.Map<RegisterUserCommand>(registerUserDto);

                // Execute the command
                var result = await _mediator.Send(command);

                _logger.LogInformation("User successfully registered with ID: {UserId}", result.User.UserId);

                return CreatedAtAction(
                    nameof(Register),
                    new { id = result.User.UserId },
                    result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning("Invalid argument during registration: {Message}", ex.Message);
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid Input",
                    Detail = ex.Message,
                    Status = StatusCodes.Status400BadRequest
                });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning("Invalid operation during registration: {Message}", ex.Message);
                return Conflict(new ProblemDetails
                {
                    Title = "Registration Conflict",
                    Detail = ex.Message,
                    Status = StatusCodes.Status409Conflict
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during user registration for email: {Email}", registerUserDto.Email);
                return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
                {
                    Title = "Internal Server Error",
                    Detail = "An unexpected error occurred while processing your request.",
                    Status = StatusCodes.Status500InternalServerError
                });
            }
        }

        /// <summary>
        /// Authenticates a user and returns JWT tokens
        /// </summary>
        /// <param name="loginUserDto">User login credentials</param>
        /// <returns>Authenticated user with tokens</returns>
        /// <response code="200">User successfully authenticated</response>
        /// <response code="400">Invalid input data or validation errors</response>
        /// <response code="401">Invalid credentials or account locked</response>
        /// <response code="429">Too many login attempts - rate limited</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("login")]
        [ProducesResponseType(typeof(AuthenticatedUserDto), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status429TooManyRequests)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<AuthenticatedUserDto>> Login([FromBody] LoginUserDto loginUserDto)
        {
            try
            {
                _logger.LogInformation("User login attempt for email: {Email}",
                    MaskEmailForLogging(loginUserDto.Email));

                // Map DTO to command and add request context
                var command = _mapper.Map<LoginUserCommand>(loginUserDto);
                command.IpAddress = GetClientIpAddress();
                command.UserAgent = Request.Headers.UserAgent.ToString();

                // Execute the command
                var result = await _mediator.Send(command);

                _logger.LogInformation("User successfully logged in with ID: {UserId}", result.User.UserId);

                return Ok(result);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning("Unauthorized login attempt for email: {Email} - {Message}",
                    MaskEmailForLogging(loginUserDto.Email), ex.Message);
                return Unauthorized(new ProblemDetails
                {
                    Title = "Authentication Failed",
                    Detail = ex.Message,
                    Status = StatusCodes.Status401Unauthorized
                });
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("Too many"))
            {
                _logger.LogWarning("Rate limit exceeded for login attempt: {Message}", ex.Message);
                return StatusCode(StatusCodes.Status429TooManyRequests, new ProblemDetails
                {
                    Title = "Too Many Requests",
                    Detail = ex.Message,
                    Status = StatusCodes.Status429TooManyRequests
                });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning("Invalid argument during login: {Message}", ex.Message);
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid Input",
                    Detail = ex.Message,
                    Status = StatusCodes.Status400BadRequest
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during user login for email: {Email}",
                    MaskEmailForLogging(loginUserDto.Email));
                return StatusCode(StatusCodes.Status500InternalServerError, new ProblemDetails
                {
                    Title = "Internal Server Error",
                    Detail = "An unexpected error occurred while processing your request.",
                    Status = StatusCodes.Status500InternalServerError
                });
            }
        }

        /// <summary>
        /// Health check endpoint for the authentication service
        /// </summary>
        /// <returns>Service health status</returns>
        /// <response code="200">Service is healthy</response>
        [HttpGet("health")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        public ActionResult<object> Health()
        {
            return Ok(new
            {
                Status = "Healthy",
                Timestamp = DateTime.UtcNow,
                Service = "Authentication"
            });
        }

        /// <summary>
        /// Gets the client IP address from the request
        /// </summary>
        /// <returns>Client IP address</returns>
        private string? GetClientIpAddress()
        {
            // Check for forwarded IP first (in case of proxy/load balancer)
            var forwardedFor = Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrWhiteSpace(forwardedFor))
            {
                // X-Forwarded-For can contain multiple IPs, take the first one
                return forwardedFor.Split(',')[0].Trim();
            }

            // Check for real IP header
            var realIp = Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrWhiteSpace(realIp))
            {
                return realIp;
            }

            // Fall back to connection remote IP
            return HttpContext.Connection.RemoteIpAddress?.ToString();
        }
        
        /// <summary>
        /// Masks email address for logging purposes
        /// </summary>
        /// <param name="email">The email to mask</param>
        /// <returns>Masked email</returns>
        private static string MaskEmailForLogging(string email)
        {
            if (string.IsNullOrWhiteSpace(email) || !email.Contains('@'))
                return "***";

            var atIndex = email.IndexOf('@');
            var localPart = email.Substring(0, atIndex);
            var domain = email.Substring(atIndex);
            
            var maskedLocal = localPart.Length <= 2 
                ? new string('*', localPart.Length)
                : localPart.Substring(0, 2) + new string('*', localPart.Length - 2);

            return maskedLocal + domain;
        }
    }
}
